<?php

namespace Pack\LaravelGeo\Database\seeders;

use Illuminate\Database\Seeder;

class StatesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        \DB::table('states')->delete();
        
        \DB::table('states')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'Southern Nations, Nationalities, and Peoples\' Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'SN',
                'translations' => '{"en":"Southern Nations, Nationalities, and Peoples\' Region","ar":"Southern Nations, Nationalities, and Peoples\' منطقة","fr":"Southern Nations, Nationalities, and Peoples\' Région","es":"Southern Nations, Nationalities, and Peoples\' Región","de":"Southern Nations, Nationalities, and Peoples\' Region"}',
                'latitude' => '6.51569110',
                'longitude' => '36.95410700',
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'Somali Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'SO',
                'translations' => '{"en":"Somali Region","ar":"Somali منطقة","fr":"Somali Région","es":"Somali Región","de":"Somali Region"}',
                'latitude' => '6.66122930',
                'longitude' => '43.79084530',
            ),
            2 => 
            array (
                'id' => 3,
                'name' => 'Amhara Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'AM',
                'translations' => '{"en":"Amhara Region","ar":"Amhara منطقة","fr":"Amhara Région","es":"Amhara Región","de":"Amhara Region"}',
                'latitude' => '11.34942470',
                'longitude' => '37.97845850',
            ),
            3 => 
            array (
                'id' => 4,
                'name' => 'Tigray Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'TI',
                'translations' => '{"en":"Tigray Region","ar":"Tigray منطقة","fr":"Tigray Région","es":"Tigray Región","de":"Tigray Region"}',
                'latitude' => '14.03233360',
                'longitude' => '38.31657250',
            ),
            4 => 
            array (
                'id' => 5,
                'name' => 'Oromia Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'OR',
                'translations' => '{"en":"Oromia Region","ar":"Oromia منطقة","fr":"Oromia Région","es":"Oromia Región","de":"Oromia Region"}',
                'latitude' => '7.54603770',
                'longitude' => '40.63468510',
            ),
            5 => 
            array (
                'id' => 6,
                'name' => 'Afar Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'AF',
                'translations' => '{"en":"Afar Region","ar":"Afar منطقة","fr":"Afar Région","es":"Afar Región","de":"Afar Region"}',
                'latitude' => '11.75593880',
                'longitude' => '40.95868800',
            ),
            6 => 
            array (
                'id' => 7,
                'name' => 'Harari Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'HA',
                'translations' => '{"en":"Harari Region","ar":"Harari منطقة","fr":"Harari Région","es":"Harari Región","de":"Harari Region"}',
                'latitude' => '9.31486600',
                'longitude' => '42.19677160',
            ),
            7 => 
            array (
                'id' => 8,
                'name' => 'Dire Dawa',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'DD',
                'translations' => '{"en":"Dire Dawa","ar":"Dire Dawa","fr":"Dire Dawa","es":"Dire Dawa","de":"Dire Dawa"}',
                'latitude' => '9.60087470',
                'longitude' => '41.85014200',
            ),
            8 => 
            array (
                'id' => 9,
                'name' => 'Benishangul-Gumuz Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'BE',
                'translations' => '{"en":"Benishangul-Gumuz Region","ar":"Benishangul-Gumuz منطقة","fr":"Benishangul-Gumuz Région","es":"Benishangul-Gumuz Región","de":"Benishangul-Gumuz Region"}',
                'latitude' => '10.78028890',
                'longitude' => '35.56578620',
            ),
            9 => 
            array (
                'id' => 10,
                'name' => 'Gambela Region',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'GA',
                'translations' => '{"en":"Gambela Region","ar":"Gambela منطقة","fr":"Gambela Région","es":"Gambela Región","de":"Gambela Region"}',
                'latitude' => '7.92196870',
                'longitude' => '34.15319470',
            ),
            10 => 
            array (
                'id' => 11,
                'name' => 'Addis Ababa',
                'country_id' => 70,
                'country_code' => 'ET',
                'iso2' => 'AA',
                'translations' => '{"en":"Addis Ababa","ar":"Addis Ababa","fr":"Addis Ababa","es":"Addis Ababa","de":"Addis Ababa"}',
                'latitude' => '8.98060340',
                'longitude' => '38.75776050',
            ),
            11 => 
            array (
                'id' => 12,
                'name' => 'Petnjica Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '23',
                'translations' => '{"en":"Petnjica Municipality","ar":"Petnjica بلدية","fr":"Petnjica Municipalité","es":"Petnjica Municipio","de":"Petnjica Gemeinde"}',
                'latitude' => '42.93534800',
                'longitude' => '20.02114490',
            ),
            12 => 
            array (
                'id' => 13,
                'name' => 'Bar Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '02',
                'translations' => '{"en":"Bar Municipality","ar":"Bar بلدية","fr":"Bar Municipalité","es":"Bar Municipio","de":"Bar Gemeinde"}',
                'latitude' => '42.12781190',
                'longitude' => '19.14043800',
            ),
            13 => 
            array (
                'id' => 14,
                'name' => 'Danilovgrad Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '07',
                'translations' => '{"en":"Danilovgrad Municipality","ar":"Danilovgrad بلدية","fr":"Danilovgrad Municipalité","es":"Danilovgrad Municipio","de":"Danilovgrad Gemeinde"}',
                'latitude' => '42.58357000',
                'longitude' => '19.14043800',
            ),
            14 => 
            array (
                'id' => 15,
                'name' => 'Rožaje Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '17',
                'translations' => '{"en":"Rožaje Municipality","ar":"Rožaje بلدية","fr":"Rožaje Municipalité","es":"Rožaje Municipio","de":"Rožaje Gemeinde"}',
                'latitude' => '42.84083890',
                'longitude' => '20.16706280',
            ),
            15 => 
            array (
                'id' => 16,
                'name' => 'Plužine Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '15',
                'translations' => '{"en":"Plužine Municipality","ar":"Plužine بلدية","fr":"Plužine Municipalité","es":"Plužine Municipio","de":"Plužine Gemeinde"}',
                'latitude' => '43.15933840',
                'longitude' => '18.85514840',
            ),
            16 => 
            array (
                'id' => 17,
                'name' => 'Nikšić Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '12',
                'translations' => '{"en":"Nikšić Municipality","ar":"Nikšić بلدية","fr":"Nikšić Municipalité","es":"Nikšić Municipio","de":"Nikšić Gemeinde"}',
                'latitude' => '42.79971840',
                'longitude' => '18.76009630',
            ),
            17 => 
            array (
                'id' => 18,
                'name' => 'Šavnik Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '18',
                'translations' => '{"en":"Šavnik Municipality","ar":"Šavnik بلدية","fr":"Šavnik Municipalité","es":"Šavnik Municipio","de":"Šavnik Gemeinde"}',
                'latitude' => '42.96037560',
                'longitude' => '19.14043800',
            ),
            18 => 
            array (
                'id' => 19,
                'name' => 'Plav Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '13',
                'translations' => '{"en":"Plav Municipality","ar":"Plav بلدية","fr":"Plav Municipalité","es":"Plav Municipio","de":"Plav Gemeinde"}',
                'latitude' => '42.60013370',
                'longitude' => '19.94075410',
            ),
            19 => 
            array (
                'id' => 20,
                'name' => 'Pljevlja Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '14',
                'translations' => '{"en":"Pljevlja Municipality","ar":"Pljevlja بلدية","fr":"Pljevlja Municipalité","es":"Pljevlja Municipio","de":"Pljevlja Gemeinde"}',
                'latitude' => '43.27233830',
                'longitude' => '19.28315310',
            ),
            20 => 
            array (
                'id' => 21,
                'name' => 'Berane Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '03',
                'translations' => '{"en":"Berane Municipality","ar":"Berane بلدية","fr":"Berane Municipalité","es":"Berane Municipio","de":"Berane Gemeinde"}',
                'latitude' => '42.82572890',
                'longitude' => '19.90205090',
            ),
            21 => 
            array (
                'id' => 22,
                'name' => 'Mojkovac Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '11',
                'translations' => '{"en":"Mojkovac Municipality","ar":"Mojkovac بلدية","fr":"Mojkovac Municipalité","es":"Mojkovac Municipio","de":"Mojkovac Gemeinde"}',
                'latitude' => '42.96880180',
                'longitude' => '19.52110630',
            ),
            22 => 
            array (
                'id' => 23,
                'name' => 'Andrijevica Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '01',
                'translations' => '{"en":"Andrijevica Municipality","ar":"Andrijevica بلدية","fr":"Andrijevica Municipalité","es":"Andrijevica Municipio","de":"Andrijevica Gemeinde"}',
                'latitude' => '42.73624770',
                'longitude' => '19.78595560',
            ),
            23 => 
            array (
                'id' => 24,
                'name' => 'Gusinje Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '22',
                'translations' => '{"en":"Gusinje Municipality","ar":"Gusinje بلدية","fr":"Gusinje Municipalité","es":"Gusinje Municipio","de":"Gusinje Gemeinde"}',
                'latitude' => '42.55634550',
                'longitude' => '19.83060510',
            ),
            24 => 
            array (
                'id' => 25,
                'name' => 'Bijelo Polje Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '04',
                'translations' => '{"en":"Bijelo Polje Municipality","ar":"Bijelo Polje بلدية","fr":"Bijelo Polje Municipalité","es":"Bijelo Polje Municipio","de":"Bijelo Polje Gemeinde"}',
                'latitude' => '43.08465260',
                'longitude' => '19.71154720',
            ),
            25 => 
            array (
                'id' => 26,
                'name' => 'Kotor Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '10',
                'translations' => '{"en":"Kotor Municipality","ar":"Kotor بلدية","fr":"Kotor Municipalité","es":"Kotor Municipio","de":"Kotor Gemeinde"}',
                'latitude' => '42.57402610',
                'longitude' => '18.64131450',
            ),
            26 => 
            array (
                'id' => 27,
                'name' => 'Podgorica Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '16',
                'translations' => '{"en":"Podgorica Municipality","ar":"Podgorica بلدية","fr":"Podgorica Municipalité","es":"Podgorica Municipio","de":"Podgorica Gemeinde"}',
                'latitude' => '42.36938340',
                'longitude' => '19.28315310',
            ),
            27 => 
            array (
                'id' => 28,
                'name' => 'Old Royal Capital Cetinje',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '06',
                'translations' => '{"en":"Old Royal Capital Cetinje","ar":"Old Royal Capital Cetinje","fr":"Old Royal Capital Cetinje","es":"Old Royal Capital Cetinje","de":"Old Royal Capital Cetinje"}',
                'latitude' => '42.39309590',
                'longitude' => '18.91159640',
            ),
            28 => 
            array (
                'id' => 29,
                'name' => 'Tivat Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '19',
                'translations' => '{"en":"Tivat Municipality","ar":"Tivat بلدية","fr":"Tivat Municipalité","es":"Tivat Municipio","de":"Tivat Gemeinde"}',
                'latitude' => '42.42348000',
                'longitude' => '18.71851840',
            ),
            29 => 
            array (
                'id' => 30,
                'name' => 'Budva Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '05',
                'translations' => '{"en":"Budva Municipality","ar":"Budva بلدية","fr":"Budva Municipalité","es":"Budva Municipio","de":"Budva Gemeinde"}',
                'latitude' => '42.31407200',
                'longitude' => '18.83138320',
            ),
            30 => 
            array (
                'id' => 31,
                'name' => 'Kolašin Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '09',
                'translations' => '{"en":"Kolašin Municipality","ar":"Kolašin بلدية","fr":"Kolašin Municipalité","es":"Kolašin Municipio","de":"Kolašin Gemeinde"}',
                'latitude' => '42.76019160',
                'longitude' => '19.42591140',
            ),
            31 => 
            array (
                'id' => 32,
                'name' => 'Žabljak Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '21',
                'translations' => '{"en":"Žabljak Municipality","ar":"Žabljak بلدية","fr":"Žabljak Municipalité","es":"Žabljak Municipio","de":"Žabljak Gemeinde"}',
                'latitude' => '43.15551520',
                'longitude' => '19.12260180',
            ),
            32 => 
            array (
                'id' => 33,
                'name' => 'Ulcinj Municipality',
                'country_id' => 147,
                'country_code' => 'ME',
                'iso2' => '20',
                'translations' => '{"en":"Ulcinj Municipality","ar":"Ulcinj بلدية","fr":"Ulcinj Municipalité","es":"Ulcinj Municipio","de":"Ulcinj Gemeinde"}',
                'latitude' => '41.96527950',
                'longitude' => '19.30694320',
            ),
            33 => 
            array (
                'id' => 34,
                'name' => 'Kunene Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'KU',
                'translations' => '{"en":"Kunene Region","ar":"Kunene منطقة","fr":"Kunene Région","es":"Kunene Región","de":"Kunene Region"}',
                'latitude' => '-19.40863170',
                'longitude' => '13.91439900',
            ),
            34 => 
            array (
                'id' => 35,
                'name' => 'Kavango West Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'KW',
                'translations' => '{"en":"Kavango West Region","ar":"Kavango West منطقة","fr":"Kavango West Région","es":"Kavango West Región","de":"Kavango West Region"}',
                'latitude' => '-18.27104800',
                'longitude' => '18.42760470',
            ),
            35 => 
            array (
                'id' => 36,
                'name' => 'Kavango East Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'KE',
                'translations' => '{"en":"Kavango East Region","ar":"Kavango East منطقة","fr":"Kavango East Région","es":"Kavango East Región","de":"Kavango East Region"}',
                'latitude' => '-18.27104800',
                'longitude' => '18.42760470',
            ),
            36 => 
            array (
                'id' => 37,
                'name' => 'Oshana Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'ON',
                'translations' => '{"en":"Oshana Region","ar":"Oshana منطقة","fr":"Oshana Région","es":"Oshana Región","de":"Oshana Region"}',
                'latitude' => '-18.43050640',
                'longitude' => '15.68817880',
            ),
            37 => 
            array (
                'id' => 38,
                'name' => 'Hardap Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'HA',
                'translations' => '{"en":"Hardap Region","ar":"Hardap منطقة","fr":"Hardap Région","es":"Hardap Región","de":"Hardap Region"}',
                'latitude' => '-24.23101340',
                'longitude' => '17.66888700',
            ),
            38 => 
            array (
                'id' => 39,
                'name' => 'Omusati Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'OS',
                'translations' => '{"en":"Omusati Region","ar":"Omusati منطقة","fr":"Omusati Région","es":"Omusati Región","de":"Omusati Region"}',
                'latitude' => '-18.40702940',
                'longitude' => '14.84546190',
            ),
            39 => 
            array (
                'id' => 40,
                'name' => 'Ohangwena Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'OW',
                'translations' => '{"en":"Ohangwena Region","ar":"Ohangwena منطقة","fr":"Ohangwena Région","es":"Ohangwena Región","de":"Ohangwena Region"}',
                'latitude' => '-17.59792910',
                'longitude' => '16.81783770',
            ),
            40 => 
            array (
                'id' => 41,
                'name' => 'Omaheke Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'OH',
                'translations' => '{"en":"Omaheke Region","ar":"Omaheke منطقة","fr":"Omaheke Région","es":"Omaheke Región","de":"Omaheke Region"}',
                'latitude' => '-21.84666510',
                'longitude' => '19.18800470',
            ),
            41 => 
            array (
                'id' => 42,
                'name' => 'Oshikoto Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'OT',
                'translations' => '{"en":"Oshikoto Region","ar":"Oshikoto منطقة","fr":"Oshikoto Région","es":"Oshikoto Región","de":"Oshikoto Region"}',
                'latitude' => '-18.41525750',
                'longitude' => '16.91225100',
            ),
            42 => 
            array (
                'id' => 43,
                'name' => 'Erongo Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'ER',
                'translations' => '{"en":"Erongo Region","ar":"Erongo منطقة","fr":"Erongo Région","es":"Erongo Región","de":"Erongo Region"}',
                'latitude' => '-22.25656820',
                'longitude' => '15.40680790',
            ),
            43 => 
            array (
                'id' => 44,
                'name' => 'Khomas Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'KH',
                'translations' => '{"en":"Khomas Region","ar":"Khomas منطقة","fr":"Khomas Région","es":"Khomas Región","de":"Khomas Region"}',
                'latitude' => '-22.63778540',
                'longitude' => '17.10119310',
            ),
            44 => 
            array (
                'id' => 45,
                'name' => 'Karas Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'KA',
                'translations' => '{"en":"Karas Region","ar":"Karas منطقة","fr":"Karas Région","es":"Karas Región","de":"Karas Region"}',
                'latitude' => '-26.84296450',
                'longitude' => '17.29028390',
            ),
            45 => 
            array (
                'id' => 46,
                'name' => 'Otjozondjupa Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'OD',
                'translations' => '{"en":"Otjozondjupa Region","ar":"Otjozondjupa منطقة","fr":"Otjozondjupa Région","es":"Otjozondjupa Región","de":"Otjozondjupa Region"}',
                'latitude' => '-20.54869160',
                'longitude' => '17.66888700',
            ),
            46 => 
            array (
                'id' => 47,
                'name' => 'Zambezi Region',
                'country_id' => 152,
                'country_code' => 'NA',
                'iso2' => 'CA',
                'translations' => '{"en":"Zambezi Region","ar":"Zambezi منطقة","fr":"Zambezi Région","es":"Zambezi Región","de":"Zambezi Region"}',
                'latitude' => '-17.81934190',
                'longitude' => '23.95364660',
            ),
            47 => 
            array (
                'id' => 48,
                'name' => 'Ashanti Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'AH',
                'translations' => '{"en":"Ashanti Region","ar":"Ashanti منطقة","fr":"Ashanti Région","es":"Ashanti Región","de":"Ashanti Region"}',
                'latitude' => '6.74704360',
                'longitude' => '-1.52086240',
            ),
            48 => 
            array (
                'id' => 49,
                'name' => 'Western Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'WP',
                'translations' => '{"en":"Western Region","ar":"Western منطقة","fr":"Western Région","es":"Western Región","de":"Western Region"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'name' => 'Eastern Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'EP',
                'translations' => '{"en":"Eastern Region","ar":"Eastern منطقة","fr":"Eastern Région","es":"Eastern Región","de":"Eastern Region"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'name' => 'Northern Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'NP',
                'translations' => '{"en":"Northern Region","ar":"Northern منطقة","fr":"Northern Région","es":"Northern Región","de":"Northern Region"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'name' => 'Central Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'CP',
                'translations' => '{"en":"Central Region","ar":"Central منطقة","fr":"Central Région","es":"Central Región","de":"Central Region"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'name' => 'Brong-Ahafo Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'BA',
                'translations' => '{"en":"Brong-Ahafo Region","ar":"Brong-Ahafo منطقة","fr":"Brong-Ahafo Région","es":"Brong-Ahafo Región","de":"Brong-Ahafo Region"}',
                'latitude' => '7.95592470',
                'longitude' => '-1.67606910',
            ),
            53 => 
            array (
                'id' => 54,
                'name' => 'Greater Accra Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'AA',
                'translations' => '{"en":"Greater Accra Region","ar":"Greater Accra منطقة","fr":"Greater Accra Région","es":"Greater Accra Región","de":"Greater Accra Region"}',
                'latitude' => '5.81428360',
                'longitude' => '0.07467670',
            ),
            54 => 
            array (
                'id' => 55,
                'name' => 'Upper East Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'UE',
                'translations' => '{"en":"Upper East Region","ar":"Upper East منطقة","fr":"Upper East Région","es":"Upper East Región","de":"Upper East Region"}',
                'latitude' => '10.70824990',
                'longitude' => '-0.98206680',
            ),
            55 => 
            array (
                'id' => 56,
                'name' => 'Volta Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'TV',
                'translations' => '{"en":"Volta Region","ar":"Volta منطقة","fr":"Volta Région","es":"Volta Región","de":"Volta Region"}',
                'latitude' => '6.57813730',
                'longitude' => '0.45023680',
            ),
            56 => 
            array (
                'id' => 57,
                'name' => 'Upper West Region',
                'country_id' => 83,
                'country_code' => 'GH',
                'iso2' => 'UW',
                'translations' => '{"en":"Upper West Region","ar":"Upper West منطقة","fr":"Upper West Région","es":"Upper West Región","de":"Upper West Region"}',
                'latitude' => '10.25297570',
                'longitude' => '-2.14502450',
            ),
            57 => 
            array (
                'id' => 58,
                'name' => 'San Marino',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '07',
                'translations' => '{"en":"San Marino","ar":"San Marino","fr":"San Marino","es":"San Marino","de":"San Marino"}',
                'latitude' => '43.94236000',
                'longitude' => '12.45777700',
            ),
            58 => 
            array (
                'id' => 59,
                'name' => 'Acquaviva',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '01',
                'translations' => '{"en":"Acquaviva","ar":"Acquaviva","fr":"Acquaviva","es":"Acquaviva","de":"Acquaviva"}',
                'latitude' => '41.86715970',
                'longitude' => '14.74694790',
            ),
            59 => 
            array (
                'id' => 60,
                'name' => 'Chiesanuova',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '02',
                'translations' => '{"en":"Chiesanuova","ar":"Chiesanuova","fr":"Chiesanuova","es":"Chiesanuova","de":"Chiesanuova"}',
                'latitude' => '45.42261720',
                'longitude' => '7.65038540',
            ),
            60 => 
            array (
                'id' => 61,
                'name' => 'Borgo Maggiore',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '06',
                'translations' => '{"en":"Borgo Maggiore","ar":"Borgo Maggiore","fr":"Borgo Maggiore","es":"Borgo Maggiore","de":"Borgo Maggiore"}',
                'latitude' => '43.95748820',
                'longitude' => '12.45525460',
            ),
            61 => 
            array (
                'id' => 62,
                'name' => 'Faetano',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '04',
                'translations' => '{"en":"Faetano","ar":"Faetano","fr":"Faetano","es":"Faetano","de":"Faetano"}',
                'latitude' => '43.93489670',
                'longitude' => '12.48965540',
            ),
            62 => 
            array (
                'id' => 63,
                'name' => 'Montegiardino',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '08',
                'translations' => '{"en":"Montegiardino","ar":"Montegiardino","fr":"Montegiardino","es":"Montegiardino","de":"Montegiardino"}',
                'latitude' => '43.90529990',
                'longitude' => '12.48105420',
            ),
            63 => 
            array (
                'id' => 64,
                'name' => 'Domagnano',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '03',
                'translations' => '{"en":"Domagnano","ar":"Domagnano","fr":"Domagnano","es":"Domagnano","de":"Domagnano"}',
                'latitude' => '43.95019290',
                'longitude' => '12.46815370',
            ),
            64 => 
            array (
                'id' => 65,
                'name' => 'Serravalle',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '09',
                'translations' => '{"en":"Serravalle","ar":"Serravalle","fr":"Serravalle","es":"Serravalle","de":"Serravalle"}',
                'latitude' => '44.72320840',
                'longitude' => '8.85740050',
            ),
            65 => 
            array (
                'id' => 66,
                'name' => 'Fiorentino',
                'country_id' => 192,
                'country_code' => 'SM',
                'iso2' => '05',
                'translations' => '{"en":"Fiorentino","ar":"Fiorentino","fr":"Fiorentino","es":"Fiorentino","de":"Fiorentino"}',
                'latitude' => '43.90783370',
                'longitude' => '12.45812090',
            ),
            66 => 
            array (
                'id' => 67,
                'name' => 'Tillabéri Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '6',
                'translations' => '{"en":"Tillabéri Region","ar":"Tillabéri منطقة","fr":"Tillabéri Région","es":"Tillabéri Región","de":"Tillabéri Region"}',
                'latitude' => '14.64895250',
                'longitude' => '2.14502450',
            ),
            67 => 
            array (
                'id' => 68,
                'name' => 'Dosso Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '3',
                'translations' => '{"en":"Dosso Region","ar":"Dosso منطقة","fr":"Dosso Région","es":"Dosso Región","de":"Dosso Region"}',
                'latitude' => '13.15139470',
                'longitude' => '3.41955270',
            ),
            68 => 
            array (
                'id' => 69,
                'name' => 'Zinder Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '7',
                'translations' => '{"en":"Zinder Region","ar":"Zinder منطقة","fr":"Zinder Région","es":"Zinder Región","de":"Zinder Region"}',
                'latitude' => '15.17188810',
                'longitude' => '10.26001250',
            ),
            69 => 
            array (
                'id' => 70,
                'name' => 'Maradi Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '4',
                'translations' => '{"en":"Maradi Region","ar":"Maradi منطقة","fr":"Maradi Région","es":"Maradi Región","de":"Maradi Region"}',
                'latitude' => '13.80180740',
                'longitude' => '7.43813550',
            ),
            70 => 
            array (
                'id' => 71,
                'name' => 'Agadez Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '1',
                'translations' => '{"en":"Agadez Region","ar":"Agadez منطقة","fr":"Agadez Région","es":"Agadez Región","de":"Agadez Region"}',
                'latitude' => '20.66707520',
                'longitude' => '12.07182810',
            ),
            71 => 
            array (
                'id' => 72,
                'name' => 'Diffa Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '2',
                'translations' => '{"en":"Diffa Region","ar":"Diffa منطقة","fr":"Diffa Région","es":"Diffa Región","de":"Diffa Region"}',
                'latitude' => '13.67686470',
                'longitude' => '12.71351210',
            ),
            72 => 
            array (
                'id' => 73,
                'name' => 'Tahoua Region',
                'country_id' => 160,
                'country_code' => 'NE',
                'iso2' => '5',
                'translations' => '{"en":"Tahoua Region","ar":"Tahoua منطقة","fr":"Tahoua Région","es":"Tahoua Región","de":"Tahoua Region"}',
                'latitude' => '16.09025430',
                'longitude' => '5.39395510',
            ),
            73 => 
            array (
                'id' => 74,
                'name' => 'Mqabba',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '33',
                'translations' => '{"en":"Mqabba","ar":"Mqabba","fr":"Mqabba","es":"Mqabba","de":"Mqabba"}',
                'latitude' => '35.84441430',
                'longitude' => '14.46941860',
            ),
            74 => 
            array (
                'id' => 75,
                'name' => 'San Ġwann',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '49',
                'translations' => '{"en":"San Ġwann","ar":"San Ġwann","fr":"San Ġwann","es":"San Ġwann","de":"San Ġwann"}',
                'latitude' => '35.90773650',
                'longitude' => '14.47524160',
            ),
            75 => 
            array (
                'id' => 76,
                'name' => 'Żurrieq',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '68',
                'translations' => '{"en":"Żurrieq","ar":"Żurrieq","fr":"Żurrieq","es":"Żurrieq","de":"Żurrieq"}',
                'latitude' => '35.82163060',
                'longitude' => '14.48106480',
            ),
            76 => 
            array (
                'id' => 77,
                'name' => 'Luqa',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '25',
                'translations' => '{"en":"Luqa","ar":"Luqa","fr":"Luqa","es":"Luqa","de":"Luqa"}',
                'latitude' => '35.85828650',
                'longitude' => '14.48688830',
            ),
            77 => 
            array (
                'id' => 78,
                'name' => 'Marsaxlokk',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '28',
                'translations' => '{"en":"Marsaxlokk","ar":"Marsaxlokk","fr":"Marsaxlokk","es":"Marsaxlokk","de":"Marsaxlokk"}',
                'latitude' => '35.84116990',
                'longitude' => '14.53930970',
            ),
            78 => 
            array (
                'id' => 79,
                'name' => 'Qala',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '42',
                'translations' => '{"en":"Qala","ar":"Qala","fr":"Qala","es":"Qala","de":"Qala"}',
                'latitude' => '36.03886280',
                'longitude' => '14.31810100',
            ),
            79 => 
            array (
                'id' => 80,
                'name' => 'Żebbuġ Malta',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '66',
                'translations' => '{"en":"Żebbuġ Malta","ar":"Żebbuġ Malta","fr":"Żebbuġ Malta","es":"Żebbuġ Malta","de":"Żebbuġ Malta"}',
                'latitude' => '35.87646480',
                'longitude' => '14.43908400',
            ),
            80 => 
            array (
                'id' => 81,
                'name' => 'Xgħajra',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '63',
                'translations' => '{"en":"Xgħajra","ar":"Xgħajra","fr":"Xgħajra","es":"Xgħajra","de":"Xgħajra"}',
                'latitude' => '35.88682820',
                'longitude' => '14.54723910',
            ),
            81 => 
            array (
                'id' => 82,
                'name' => 'Kirkop',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '23',
                'translations' => '{"en":"Kirkop","ar":"Kirkop","fr":"Kirkop","es":"Kirkop","de":"Kirkop"}',
                'latitude' => '35.84378620',
                'longitude' => '14.48543240',
            ),
            82 => 
            array (
                'id' => 83,
                'name' => 'Rabat',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '46',
                'translations' => '{"en":"Rabat","ar":"Rabat","fr":"Rabat","es":"Rabat","de":"Rabat"}',
                'latitude' => '33.97159040',
                'longitude' => '-6.84981290',
            ),
            83 => 
            array (
                'id' => 84,
                'name' => 'Floriana',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '09',
                'translations' => '{"en":"Floriana","ar":"Floriana","fr":"Floriana","es":"Floriana","de":"Floriana"}',
                'latitude' => '45.49521850',
                'longitude' => '-73.71395760',
            ),
            84 => 
            array (
                'id' => 85,
                'name' => 'Żebbuġ Gozo',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '65',
                'translations' => '{"en":"Żebbuġ Gozo","ar":"Żebbuġ Gozo","fr":"Żebbuġ Gozo","es":"Żebbuġ Gozo","de":"Żebbuġ Gozo"}',
                'latitude' => '36.07164030',
                'longitude' => '14.24540800',
            ),
            85 => 
            array (
                'id' => 86,
                'name' => 'Swieqi',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '57',
                'translations' => '{"en":"Swieqi","ar":"Swieqi","fr":"Swieqi","es":"Swieqi","de":"Swieqi"}',
                'latitude' => '35.91911820',
                'longitude' => '14.46941860',
            ),
            86 => 
            array (
                'id' => 87,
                'name' => 'Saint Lawrence',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '50',
                'translations' => '{"en":"Saint Lawrence","ar":"Saint Lawrence","fr":"Saint Lawrence","es":"Saint Lawrence","de":"Saint Lawrence"}',
                'latitude' => '38.95780560',
                'longitude' => '-95.25656890',
            ),
            87 => 
            array (
                'id' => 88,
                'name' => 'Birżebbuġa',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '05',
                'translations' => '{"en":"Birżebbuġa","ar":"Birżebbuġa","fr":"Birżebbuġa","es":"Birżebbuġa","de":"Birżebbuġa"}',
                'latitude' => '35.81359890',
                'longitude' => '14.52474630',
            ),
            88 => 
            array (
                'id' => 89,
                'name' => 'Mdina',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '29',
                'translations' => '{"en":"Mdina","ar":"Mdina","fr":"Mdina","es":"Mdina","de":"Mdina"}',
                'latitude' => '35.88809300',
                'longitude' => '14.40683570',
            ),
            89 => 
            array (
                'id' => 90,
                'name' => 'Santa Venera',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '54',
                'translations' => '{"en":"Santa Venera","ar":"Santa Venera","fr":"Santa Venera","es":"Santa Venera","de":"Santa Venera"}',
                'latitude' => '35.89022010',
                'longitude' => '14.47669740',
            ),
            90 => 
            array (
                'id' => 91,
                'name' => 'Kerċem',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '22',
                'translations' => '{"en":"Kerċem","ar":"Kerċem","fr":"Kerċem","es":"Kerċem","de":"Kerċem"}',
                'latitude' => '36.04479390',
                'longitude' => '14.22506050',
            ),
            91 => 
            array (
                'id' => 92,
                'name' => 'Għarb',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '14',
                'translations' => '{"en":"Għarb","ar":"Għarb","fr":"Għarb","es":"Għarb","de":"Għarb"}',
                'latitude' => '36.06890900',
                'longitude' => '14.20180980',
            ),
            92 => 
            array (
                'id' => 93,
                'name' => 'Iklin',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '19',
                'translations' => '{"en":"Iklin","ar":"Iklin","fr":"Iklin","es":"Iklin","de":"Iklin"}',
                'latitude' => '35.90987740',
                'longitude' => '14.45777320',
            ),
            93 => 
            array (
                'id' => 94,
                'name' => 'Santa Luċija',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '53',
                'translations' => '{"en":"Santa Luċija","ar":"Santa Luċija","fr":"Santa Luċija","es":"Santa Luċija","de":"Santa Luċija"}',
                'latitude' => '35.85614200',
                'longitude' => '14.50436000',
            ),
            94 => 
            array (
                'id' => 95,
                'name' => 'Valletta',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '60',
                'translations' => '{"en":"Valletta","ar":"Valletta","fr":"Valletta","es":"Valletta","de":"Valletta"}',
                'latitude' => '35.89890850',
                'longitude' => '14.51455280',
            ),
            95 => 
            array (
                'id' => 96,
                'name' => 'Msida',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '34',
                'translations' => '{"en":"Msida","ar":"Msida","fr":"Msida","es":"Msida","de":"Msida"}',
                'latitude' => '35.89563880',
                'longitude' => '14.48688830',
            ),
            96 => 
            array (
                'id' => 97,
                'name' => 'Birkirkara',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '04',
                'translations' => '{"en":"Birkirkara","ar":"Birkirkara","fr":"Birkirkara","es":"Birkirkara","de":"Birkirkara"}',
                'latitude' => '35.89547060',
                'longitude' => '14.46650720',
            ),
            97 => 
            array (
                'id' => 98,
                'name' => 'Siġġiewi',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '55',
                'translations' => '{"en":"Siġġiewi","ar":"Siġġiewi","fr":"Siġġiewi","es":"Siġġiewi","de":"Siġġiewi"}',
                'latitude' => '35.84637420',
                'longitude' => '14.43157460',
            ),
            98 => 
            array (
                'id' => 99,
                'name' => 'Kalkara',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '21',
                'translations' => '{"en":"Kalkara","ar":"Kalkara","fr":"Kalkara","es":"Kalkara","de":"Kalkara"}',
                'latitude' => '35.89142420',
                'longitude' => '14.53202780',
            ),
            99 => 
            array (
                'id' => 100,
                'name' => 'St. Julian\'s',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '48',
                'translations' => '{"en":"St. Julian\'s","ar":"St. Julian\'s","fr":"St. Julian\'s","es":"St. Julian\'s","de":"St. Julian\'s"}',
                'latitude' => '42.21225130',
                'longitude' => '-85.89171270',
            ),
            100 => 
            array (
                'id' => 101,
                'name' => 'Victoria',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '45',
                'translations' => '{"en":"Victoria","ar":"Victoria","fr":"Victoria","es":"Victoria","de":"Victoria"}',
                'latitude' => '28.80526740',
                'longitude' => '-97.00359820',
            ),
            101 => 
            array (
                'id' => 102,
                'name' => 'Mellieħa',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '30',
                'translations' => '{"en":"Mellieħa","ar":"Mellieħa","fr":"Mellieħa","es":"Mellieħa","de":"Mellieħa"}',
                'latitude' => '35.95235290',
                'longitude' => '14.35009750',
            ),
            102 => 
            array (
                'id' => 103,
                'name' => 'Tarxien',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '59',
                'translations' => '{"en":"Tarxien","ar":"Tarxien","fr":"Tarxien","es":"Tarxien","de":"Tarxien"}',
                'latitude' => '35.86725520',
                'longitude' => '14.51164050',
            ),
            103 => 
            array (
                'id' => 104,
                'name' => 'Sliema',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '56',
                'translations' => '{"en":"Sliema","ar":"Sliema","fr":"Sliema","es":"Sliema","de":"Sliema"}',
                'latitude' => '35.91100810',
                'longitude' => '14.50290400',
            ),
            104 => 
            array (
                'id' => 105,
                'name' => 'Ħamrun',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '18',
                'translations' => '{"en":"Ħamrun","ar":"Ħamrun","fr":"Ħamrun","es":"Ħamrun","de":"Ħamrun"}',
                'latitude' => '35.88612370',
                'longitude' => '14.48834420',
            ),
            105 => 
            array (
                'id' => 106,
                'name' => 'Għasri',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '16',
                'translations' => '{"en":"Għasri","ar":"Għasri","fr":"Għasri","es":"Għasri","de":"Għasri"}',
                'latitude' => '36.06680750',
                'longitude' => '14.21924750',
            ),
            106 => 
            array (
                'id' => 107,
                'name' => 'Birgu',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '03',
                'translations' => '{"en":"Birgu","ar":"Birgu","fr":"Birgu","es":"Birgu","de":"Birgu"}',
                'latitude' => '35.88792140',
                'longitude' => '14.52256200',
            ),
            107 => 
            array (
                'id' => 108,
                'name' => 'Balzan',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '02',
                'translations' => '{"en":"Balzan","ar":"Balzan","fr":"Balzan","es":"Balzan","de":"Balzan"}',
                'latitude' => '35.89574140',
                'longitude' => '14.45340650',
            ),
            108 => 
            array (
                'id' => 109,
                'name' => 'Mġarr',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '31',
                'translations' => '{"en":"Mġarr","ar":"Mġarr","fr":"Mġarr","es":"Mġarr","de":"Mġarr"}',
                'latitude' => '35.91893270',
                'longitude' => '14.36173430',
            ),
            109 => 
            array (
                'id' => 110,
                'name' => 'Attard',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '01',
                'translations' => '{"en":"Attard","ar":"Attard","fr":"Attard","es":"Attard","de":"Attard"}',
                'latitude' => '35.89049670',
                'longitude' => '14.41993220',
            ),
            110 => 
            array (
                'id' => 111,
                'name' => 'Qrendi',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '44',
                'translations' => '{"en":"Qrendi","ar":"Qrendi","fr":"Qrendi","es":"Qrendi","de":"Qrendi"}',
                'latitude' => '35.83284880',
                'longitude' => '14.45486210',
            ),
            111 => 
            array (
                'id' => 112,
                'name' => 'Naxxar',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '38',
                'translations' => '{"en":"Naxxar","ar":"Naxxar","fr":"Naxxar","es":"Naxxar","de":"Naxxar"}',
                'latitude' => '35.93175180',
                'longitude' => '14.43157460',
            ),
            112 => 
            array (
                'id' => 113,
                'name' => 'Gżira',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '12',
                'translations' => '{"en":"Gżira","ar":"Gżira","fr":"Gżira","es":"Gżira","de":"Gżira"}',
                'latitude' => '35.90589700',
                'longitude' => '14.49533380',
            ),
            113 => 
            array (
                'id' => 114,
                'name' => 'Xagħra',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '61',
                'translations' => '{"en":"Xagħra","ar":"Xagħra","fr":"Xagħra","es":"Xagħra","de":"Xagħra"}',
                'latitude' => '36.05084500',
                'longitude' => '14.26748200',
            ),
            114 => 
            array (
                'id' => 115,
                'name' => 'Paola',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '39',
                'translations' => '{"en":"Paola","ar":"Paola","fr":"Paola","es":"Paola","de":"Paola"}',
                'latitude' => '38.57223530',
                'longitude' => '-94.87912940',
            ),
            115 => 
            array (
                'id' => 116,
                'name' => 'Sannat',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '52',
                'translations' => '{"en":"Sannat","ar":"Sannat","fr":"Sannat","es":"Sannat","de":"Sannat"}',
                'latitude' => '36.01926430',
                'longitude' => '14.25994370',
            ),
            116 => 
            array (
                'id' => 117,
                'name' => 'Dingli',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '07',
                'translations' => '{"en":"Dingli","ar":"Dingli","fr":"Dingli","es":"Dingli","de":"Dingli"}',
                'latitude' => '35.86273090',
                'longitude' => '14.38501070',
            ),
            117 => 
            array (
                'id' => 118,
                'name' => 'Gudja',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '11',
                'translations' => '{"en":"Gudja","ar":"Gudja","fr":"Gudja","es":"Gudja","de":"Gudja"}',
                'latitude' => '35.84698030',
                'longitude' => '14.50290400',
            ),
            118 => 
            array (
                'id' => 119,
                'name' => 'Qormi',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '43',
                'translations' => '{"en":"Qormi","ar":"Qormi","fr":"Qormi","es":"Qormi","de":"Qormi"}',
                'latitude' => '35.87643880',
                'longitude' => '14.46941860',
            ),
            119 => 
            array (
                'id' => 120,
                'name' => 'Għargħur',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '15',
                'translations' => '{"en":"Għargħur","ar":"Għargħur","fr":"Għargħur","es":"Għargħur","de":"Għargħur"}',
                'latitude' => '35.92205690',
                'longitude' => '14.45631760',
            ),
            120 => 
            array (
                'id' => 121,
                'name' => 'Xewkija',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '62',
                'translations' => '{"en":"Xewkija","ar":"Xewkija","fr":"Xewkija","es":"Xewkija","de":"Xewkija"}',
                'latitude' => '36.02992360',
                'longitude' => '14.25994370',
            ),
            121 => 
            array (
                'id' => 122,
                'name' => 'Ta\' Xbiex',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '58',
                'translations' => '{"en":"Ta\' Xbiex","ar":"Ta\' Xbiex","fr":"Ta\' Xbiex","es":"Ta\' Xbiex","de":"Ta\' Xbiex"}',
                'latitude' => '35.89914480',
                'longitude' => '14.49635190',
            ),
            122 => 
            array (
                'id' => 123,
                'name' => 'Żabbar',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '64',
                'translations' => '{"en":"Żabbar","ar":"Żabbar","fr":"Żabbar","es":"Żabbar","de":"Żabbar"}',
                'latitude' => '35.87247150',
                'longitude' => '14.54513540',
            ),
            123 => 
            array (
                'id' => 124,
                'name' => 'Għaxaq',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '17',
                'translations' => '{"en":"Għaxaq","ar":"Għaxaq","fr":"Għaxaq","es":"Għaxaq","de":"Għaxaq"}',
                'latitude' => '35.84403590',
                'longitude' => '14.51600900',
            ),
            124 => 
            array (
                'id' => 125,
                'name' => 'Pembroke',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '40',
                'translations' => '{"en":"Pembroke","ar":"Pembroke","fr":"Pembroke","es":"Pembroke","de":"Pembroke"}',
                'latitude' => '34.68016260',
                'longitude' => '-79.19503730',
            ),
            125 => 
            array (
                'id' => 126,
                'name' => 'Lija',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '24',
                'translations' => '{"en":"Lija","ar":"Lija","fr":"Lija","es":"Lija","de":"Lija"}',
                'latitude' => '49.18007600',
                'longitude' => '-123.10331700',
            ),
            126 => 
            array (
                'id' => 127,
                'name' => 'Pietà',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '41',
                'translations' => '{"en":"Pietà","ar":"Pietà","fr":"Pietà","es":"Pietà","de":"Pietà"}',
                'latitude' => '42.21862000',
                'longitude' => '-83.73464700',
            ),
            127 => 
            array (
                'id' => 128,
                'name' => 'Marsa',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '26',
                'translations' => '{"en":"Marsa","ar":"Marsa","fr":"Marsa","es":"Marsa","de":"Marsa"}',
                'latitude' => '34.03195870',
                'longitude' => '-118.44555350',
            ),
            128 => 
            array (
                'id' => 129,
                'name' => 'Fgura',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '08',
                'translations' => '{"en":"Fgura","ar":"Fgura","fr":"Fgura","es":"Fgura","de":"Fgura"}',
                'latitude' => '35.87382690',
                'longitude' => '14.52329010',
            ),
            129 => 
            array (
                'id' => 130,
                'name' => 'Għajnsielem',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '13',
                'translations' => '{"en":"Għajnsielem","ar":"Għajnsielem","fr":"Għajnsielem","es":"Għajnsielem","de":"Għajnsielem"}',
                'latitude' => '36.02479660',
                'longitude' => '14.28029610',
            ),
            130 => 
            array (
                'id' => 131,
                'name' => 'Mtarfa',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '35',
                'translations' => '{"en":"Mtarfa","ar":"Mtarfa","fr":"Mtarfa","es":"Mtarfa","de":"Mtarfa"}',
                'latitude' => '35.88951250',
                'longitude' => '14.39519530',
            ),
            131 => 
            array (
                'id' => 132,
                'name' => 'Munxar',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '36',
                'translations' => '{"en":"Munxar","ar":"Munxar","fr":"Munxar","es":"Munxar","de":"Munxar"}',
                'latitude' => '36.02880580',
                'longitude' => '14.22506050',
            ),
            132 => 
            array (
                'id' => 133,
                'name' => 'Nadur',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '37',
                'translations' => '{"en":"Nadur","ar":"Nadur","fr":"Nadur","es":"Nadur","de":"Nadur"}',
                'latitude' => '36.04470190',
                'longitude' => '14.29192730',
            ),
            133 => 
            array (
                'id' => 134,
                'name' => 'Fontana',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '10',
                'translations' => '{"en":"Fontana","ar":"Fontana","fr":"Fontana","es":"Fontana","de":"Fontana"}',
                'latitude' => '34.09223350',
                'longitude' => '-117.43504800',
            ),
            134 => 
            array (
                'id' => 135,
                'name' => 'Żejtun',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '67',
                'translations' => '{"en":"Żejtun","ar":"Żejtun","fr":"Żejtun","es":"Żejtun","de":"Żejtun"}',
                'latitude' => '35.85487140',
                'longitude' => '14.53639690',
            ),
            135 => 
            array (
                'id' => 136,
                'name' => 'Senglea',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '20',
                'translations' => '{"en":"Senglea","ar":"Senglea","fr":"Senglea","es":"Senglea","de":"Senglea"}',
                'latitude' => '35.88730410',
                'longitude' => '14.51673710',
            ),
            136 => 
            array (
                'id' => 137,
                'name' => 'Marsaskala',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '27',
                'translations' => '{"en":"Marsaskala","ar":"Marsaskala","fr":"Marsaskala","es":"Marsaskala","de":"Marsaskala"}',
                'latitude' => '35.86036400',
                'longitude' => '14.55678760',
            ),
            137 => 
            array (
                'id' => 138,
                'name' => 'Cospicua',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '06',
                'translations' => '{"en":"Cospicua","ar":"Cospicua","fr":"Cospicua","es":"Cospicua","de":"Cospicua"}',
                'latitude' => '35.88067530',
                'longitude' => '14.52183380',
            ),
            138 => 
            array (
                'id' => 139,
                'name' => 'St. Paul\'s Bay',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '51',
                'translations' => '{"en":"St. Paul\'s Bay","ar":"St. Paul\'s Bay","fr":"St. Paul\'s Bay","es":"St. Paul\'s Bay","de":"St. Paul\'s Bay"}',
                'latitude' => '35.93601700',
                'longitude' => '14.39665030',
            ),
            139 => 
            array (
                'id' => 140,
                'name' => 'Mosta',
                'country_id' => 135,
                'country_code' => 'MT',
                'iso2' => '32',
                'translations' => '{"en":"Mosta","ar":"Mosta","fr":"Mosta","es":"Mosta","de":"Mosta"}',
                'latitude' => '35.91415040',
                'longitude' => '14.42284270',
            ),
            140 => 
            array (
                'id' => 141,
                'name' => 'Mangystau Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'MAN',
                'translations' => '{"en":"Mangystau Region","ar":"Mangystau منطقة","fr":"Mangystau Région","es":"Mangystau Región","de":"Mangystau Region"}',
                'latitude' => '44.59080200',
                'longitude' => '53.84995080',
            ),
            141 => 
            array (
                'id' => 142,
                'name' => 'Kyzylorda Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'KZY',
                'translations' => '{"en":"Kyzylorda Region","ar":"Kyzylorda منطقة","fr":"Kyzylorda Région","es":"Kyzylorda Región","de":"Kyzylorda Region"}',
                'latitude' => '44.69226130',
                'longitude' => '62.65718850',
            ),
            142 => 
            array (
                'id' => 143,
                'name' => 'Almaty Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'ALM',
                'translations' => '{"en":"Almaty Region","ar":"Almaty منطقة","fr":"Almaty Région","es":"Almaty Región","de":"Almaty Region"}',
                'latitude' => '45.01192270',
                'longitude' => '78.42293920',
            ),
            143 => 
            array (
                'id' => 144,
                'name' => 'North Kazakhstan Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'SEV',
                'translations' => '{"en":"North Kazakhstan Region","ar":"North Kazakhstan منطقة","fr":"North Kazakhstan Région","es":"North Kazakhstan Región","de":"North Kazakhstan Region"}',
                'latitude' => '54.16220660',
                'longitude' => '69.93870710',
            ),
            144 => 
            array (
                'id' => 145,
                'name' => 'Akmola Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'AKM',
                'translations' => '{"en":"Akmola Region","ar":"Akmola منطقة","fr":"Akmola Région","es":"Akmola Región","de":"Akmola Region"}',
                'latitude' => '51.91653200',
                'longitude' => '69.41104940',
            ),
            145 => 
            array (
                'id' => 146,
                'name' => 'Pavlodar Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'PAV',
                'translations' => '{"en":"Pavlodar Region","ar":"Pavlodar منطقة","fr":"Pavlodar Région","es":"Pavlodar Región","de":"Pavlodar Region"}',
                'latitude' => '52.28784440',
                'longitude' => '76.97334530',
            ),
            146 => 
            array (
                'id' => 147,
                'name' => 'Jambyl Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'ZHA',
                'translations' => '{"en":"Jambyl Region","ar":"Jambyl منطقة","fr":"Jambyl Région","es":"Jambyl Región","de":"Jambyl Region"}',
                'latitude' => '44.22203080',
                'longitude' => '72.36579670',
            ),
            147 => 
            array (
                'id' => 148,
                'name' => 'West Kazakhstan Province',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'ZAP',
                'translations' => '{"en":"West Kazakhstan Province","ar":"West Kazakhstan مقاطعة","fr":"West Kazakhstan Province","es":"West Kazakhstan Provincia","de":"West Kazakhstan Provinz"}',
                'latitude' => '49.56797270',
                'longitude' => '50.80666160',
            ),
            148 => 
            array (
                'id' => 149,
                'name' => 'Turkestan Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'YUZ',
                'translations' => '{"en":"Turkestan Region","ar":"Turkestan منطقة","fr":"Turkestan Région","es":"Turkestan Región","de":"Turkestan Region"}',
                'latitude' => '43.36669580',
                'longitude' => '68.40944050',
            ),
            149 => 
            array (
                'id' => 150,
                'name' => 'Karaganda Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'KAR',
                'translations' => '{"en":"Karaganda Region","ar":"Karaganda منطقة","fr":"Karaganda Région","es":"Karaganda Región","de":"Karaganda Region"}',
                'latitude' => '47.90221820',
                'longitude' => '71.77068070',
            ),
            150 => 
            array (
                'id' => 151,
                'name' => 'Aktobe Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'AKT',
                'translations' => '{"en":"Aktobe Region","ar":"Aktobe منطقة","fr":"Aktobe Région","es":"Aktobe Región","de":"Aktobe Region"}',
                'latitude' => '48.77970780',
                'longitude' => '57.99743780',
            ),
            151 => 
            array (
                'id' => 152,
                'name' => 'Almaty',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'ALA',
                'translations' => '{"en":"Almaty","ar":"Almaty","fr":"Almaty","es":"Almaty","de":"Almaty"}',
                'latitude' => '43.22201460',
                'longitude' => '76.85124850',
            ),
            152 => 
            array (
                'id' => 153,
                'name' => 'Atyrau Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'ATY',
                'translations' => '{"en":"Atyrau Region","ar":"Atyrau منطقة","fr":"Atyrau Région","es":"Atyrau Región","de":"Atyrau Region"}',
                'latitude' => '47.10761880',
                'longitude' => '51.91413300',
            ),
            153 => 
            array (
                'id' => 154,
                'name' => 'East Kazakhstan Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'VOS',
                'translations' => '{"en":"East Kazakhstan Region","ar":"East Kazakhstan منطقة","fr":"East Kazakhstan Région","es":"East Kazakhstan Región","de":"East Kazakhstan Region"}',
                'latitude' => '48.70626870',
                'longitude' => '80.79225340',
            ),
            154 => 
            array (
                'id' => 155,
                'name' => 'Baikonur',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'BAY',
                'translations' => '{"en":"Baikonur","ar":"Baikonur","fr":"Baikonur","es":"Baikonur","de":"Baikonur"}',
                'latitude' => '45.96458510',
                'longitude' => '63.30524280',
            ),
            155 => 
            array (
                'id' => 156,
                'name' => 'Nur-Sultan',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'AST',
                'translations' => '{"en":"Nur-Sultan","ar":"Nur-Sultan","fr":"Nur-Sultan","es":"Nur-Sultan","de":"Nur-Sultan"}',
                'latitude' => '51.16052270',
                'longitude' => '71.47035580',
            ),
            156 => 
            array (
                'id' => 157,
                'name' => 'Kostanay Region',
                'country_id' => 112,
                'country_code' => 'KZ',
                'iso2' => 'KUS',
                'translations' => '{"en":"Kostanay Region","ar":"Kostanay منطقة","fr":"Kostanay Région","es":"Kostanay Región","de":"Kostanay Region"}',
                'latitude' => '51.50770960',
                'longitude' => '64.04790730',
            ),
            157 => 
            array (
                'id' => 158,
                'name' => 'Kakamega County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '11',
                'translations' => '{"en":"Kakamega County","ar":"Kakamega مقاطعة","fr":"Kakamega Comté","es":"Kakamega Condado","de":"Kakamega Landkreis"}',
                'latitude' => '0.30789400',
                'longitude' => '34.77407930',
            ),
            158 => 
            array (
                'id' => 159,
                'name' => 'Kisii County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '16',
                'translations' => '{"en":"Kisii County","ar":"Kisii مقاطعة","fr":"Kisii Comté","es":"Kisii Condado","de":"Kisii Landkreis"}',
                'latitude' => '-0.67733400',
                'longitude' => '34.77960300',
            ),
            159 => 
            array (
                'id' => 160,
                'name' => 'Central Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '200',
                'translations' => '{"en":"Central Province","ar":"Central مقاطعة","fr":"Central Province","es":"Central Provincia","de":"Central Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            160 => 
            array (
                'id' => 161,
                'name' => 'Busia County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '04',
                'translations' => '{"en":"Busia County","ar":"Busia مقاطعة","fr":"Busia Comté","es":"Busia Condado","de":"Busia Landkreis"}',
                'latitude' => '0.43465060',
                'longitude' => '34.24215970',
            ),
            161 => 
            array (
                'id' => 162,
                'name' => 'North Eastern Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '500',
                'translations' => '{"en":"North Eastern Province","ar":"North Eastern مقاطعة","fr":"North Eastern Province","es":"North Eastern Provincia","de":"North Eastern Provinz"}',
                'latitude' => '7.78530510',
                'longitude' => '81.42789840',
            ),
            162 => 
            array (
                'id' => 163,
                'name' => 'Embu County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '06',
                'translations' => '{"en":"Embu County","ar":"Embu مقاطعة","fr":"Embu Comté","es":"Embu Condado","de":"Embu Landkreis"}',
                'latitude' => '-0.65604770',
                'longitude' => '37.72376780',
            ),
            163 => 
            array (
                'id' => 164,
                'name' => 'Laikipia County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '20',
                'translations' => '{"en":"Laikipia County","ar":"Laikipia مقاطعة","fr":"Laikipia Comté","es":"Laikipia Condado","de":"Laikipia Landkreis"}',
                'latitude' => '0.36060630',
                'longitude' => '36.78195050',
            ),
            164 => 
            array (
                'id' => 165,
                'name' => 'Nandi District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '32',
                'translations' => '{"en":"Nandi District","ar":"Nandi مقاطعة","fr":"Nandi District","es":"Nandi Distrito","de":"Nandi Bezirk"}',
                'latitude' => '0.18358670',
                'longitude' => '35.12687810',
            ),
            165 => 
            array (
                'id' => 166,
                'name' => 'Lamu County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '21',
                'translations' => '{"en":"Lamu County","ar":"Lamu مقاطعة","fr":"Lamu Comté","es":"Lamu Condado","de":"Lamu Landkreis"}',
                'latitude' => '-2.23550580',
                'longitude' => '40.47200040',
            ),
            166 => 
            array (
                'id' => 167,
                'name' => 'Kirinyaga County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '15',
                'translations' => '{"en":"Kirinyaga County","ar":"Kirinyaga مقاطعة","fr":"Kirinyaga Comté","es":"Kirinyaga Condado","de":"Kirinyaga Landkreis"}',
                'latitude' => '-0.65905650',
                'longitude' => '37.38272340',
            ),
            167 => 
            array (
                'id' => 168,
                'name' => 'Bungoma County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '03',
                'translations' => '{"en":"Bungoma County","ar":"Bungoma مقاطعة","fr":"Bungoma Comté","es":"Bungoma Condado","de":"Bungoma Landkreis"}',
                'latitude' => '0.56952520',
                'longitude' => '34.55837660',
            ),
            168 => 
            array (
                'id' => 169,
                'name' => 'Uasin Gishu District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '44',
                'translations' => '{"en":"Uasin Gishu District","ar":"Uasin Gishu مقاطعة","fr":"Uasin Gishu District","es":"Uasin Gishu Distrito","de":"Uasin Gishu Bezirk"}',
                'latitude' => '0.55276380',
                'longitude' => '35.30272260',
            ),
            169 => 
            array (
                'id' => 170,
                'name' => 'Isiolo County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '09',
                'translations' => '{"en":"Isiolo County","ar":"Isiolo مقاطعة","fr":"Isiolo Comté","es":"Isiolo Condado","de":"Isiolo Landkreis"}',
                'latitude' => '0.35243520',
                'longitude' => '38.48499230',
            ),
            170 => 
            array (
                'id' => 171,
                'name' => 'Kisumu County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '17',
                'translations' => '{"en":"Kisumu County","ar":"Kisumu مقاطعة","fr":"Kisumu Comté","es":"Kisumu Condado","de":"Kisumu Landkreis"}',
                'latitude' => '-0.09170160',
                'longitude' => '34.76795680',
            ),
            171 => 
            array (
                'id' => 172,
                'name' => 'Coast Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '300',
                'translations' => '{"en":"Coast Province","ar":"Coast مقاطعة","fr":"Coast Province","es":"Coast Provincia","de":"Coast Provinz"}',
                'latitude' => '39.13733050',
                'longitude' => '-76.72941440',
            ),
            172 => 
            array (
                'id' => 173,
                'name' => 'Kwale County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '19',
                'translations' => '{"en":"Kwale County","ar":"Kwale مقاطعة","fr":"Kwale Comté","es":"Kwale Condado","de":"Kwale Landkreis"}',
                'latitude' => '-4.18161150',
                'longitude' => '39.46056120',
            ),
            173 => 
            array (
                'id' => 174,
                'name' => 'Kilifi County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '14',
                'translations' => '{"en":"Kilifi County","ar":"Kilifi مقاطعة","fr":"Kilifi Comté","es":"Kilifi Condado","de":"Kilifi Landkreis"}',
                'latitude' => '-3.51065080',
                'longitude' => '39.90932690',
            ),
            174 => 
            array (
                'id' => 175,
                'name' => 'Narok County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '33',
                'translations' => '{"en":"Narok County","ar":"Narok مقاطعة","fr":"Narok Comté","es":"Narok Condado","de":"Narok Landkreis"}',
                'latitude' => '-1.10411100',
                'longitude' => '36.08934060',
            ),
            175 => 
            array (
                'id' => 176,
                'name' => 'Taita–Taveta County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '39',
                'translations' => '{"en":"Taita–Taveta County","ar":"Taita–Taveta مقاطعة","fr":"Taita–Taveta Comté","es":"Taita–Taveta Condado","de":"Taita–Taveta Landkreis"}',
                'latitude' => '-3.31606870',
                'longitude' => '38.48499230',
            ),
            176 => 
            array (
                'id' => 177,
                'name' => 'Western Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '800',
                'translations' => '{"en":"Western Province","ar":"Western مقاطعة","fr":"Western Province","es":"Western Provincia","de":"Western Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            177 => 
            array (
                'id' => 178,
                'name' => 'Muranga County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '29',
                'translations' => '{"en":"Muranga County","ar":"Muranga مقاطعة","fr":"Muranga Comté","es":"Muranga Condado","de":"Muranga Landkreis"}',
                'latitude' => '-0.78392810',
                'longitude' => '37.04003390',
            ),
            178 => 
            array (
                'id' => 179,
                'name' => 'Rift Valley Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '700',
                'translations' => '{"en":"Rift Valley Province","ar":"Rift Valley مقاطعة","fr":"Rift Valley Province","es":"Rift Valley Provincia","de":"Rift Valley Provinz"}',
                'latitude' => '11.40987370',
                'longitude' => '41.28085770',
            ),
            179 => 
            array (
                'id' => 180,
                'name' => 'Nyeri County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '36',
                'translations' => '{"en":"Nyeri County","ar":"Nyeri مقاطعة","fr":"Nyeri Comté","es":"Nyeri Condado","de":"Nyeri Landkreis"}',
                'latitude' => '-0.41969150',
                'longitude' => '37.04003390',
            ),
            180 => 
            array (
                'id' => 181,
                'name' => 'Baringo County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '01',
                'translations' => '{"en":"Baringo County","ar":"Baringo مقاطعة","fr":"Baringo Comté","es":"Baringo Condado","de":"Baringo Landkreis"}',
                'latitude' => '0.85549880',
                'longitude' => '36.08934060',
            ),
            181 => 
            array (
                'id' => 182,
                'name' => 'Wajir County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '46',
                'translations' => '{"en":"Wajir County","ar":"Wajir مقاطعة","fr":"Wajir Comté","es":"Wajir Condado","de":"Wajir Landkreis"}',
                'latitude' => '1.63604750',
                'longitude' => '40.30886260',
            ),
            182 => 
            array (
                'id' => 183,
                'name' => 'Trans-Nzoia District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '42',
                'translations' => '{"en":"Trans-Nzoia District","ar":"Trans-Nzoia مقاطعة","fr":"Trans-Nzoia District","es":"Trans-Nzoia Distrito","de":"Trans-Nzoia Bezirk"}',
                'latitude' => '1.05666670',
                'longitude' => '34.95066250',
            ),
            183 => 
            array (
                'id' => 184,
                'name' => 'Machakos County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '22',
                'translations' => '{"en":"Machakos County","ar":"Machakos مقاطعة","fr":"Machakos Comté","es":"Machakos Condado","de":"Machakos Landkreis"}',
                'latitude' => '-1.51768370',
                'longitude' => '37.26341460',
            ),
            184 => 
            array (
                'id' => 185,
                'name' => 'Tharaka Nithi County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '41',
                'translations' => '{"en":"Tharaka Nithi County","ar":"Tharaka Nithi مقاطعة","fr":"Tharaka Nithi Comté","es":"Tharaka Nithi Condado","de":"Tharaka Nithi Landkreis"}',
                'latitude' => '-0.29648510',
                'longitude' => '37.72376780',
            ),
            185 => 
            array (
                'id' => 186,
                'name' => 'Siaya County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '38',
                'translations' => '{"en":"Siaya County","ar":"Siaya مقاطعة","fr":"Siaya Comté","es":"Siaya Condado","de":"Siaya Landkreis"}',
                'latitude' => '-0.06173280',
                'longitude' => '34.24215970',
            ),
            186 => 
            array (
                'id' => 187,
                'name' => 'Mandera County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '24',
                'translations' => '{"en":"Mandera County","ar":"Mandera مقاطعة","fr":"Mandera Comté","es":"Mandera Condado","de":"Mandera Landkreis"}',
                'latitude' => '3.57379910',
                'longitude' => '40.95868800',
            ),
            187 => 
            array (
                'id' => 188,
                'name' => 'Makueni County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '23',
                'translations' => '{"en":"Makueni County","ar":"Makueni مقاطعة","fr":"Makueni Comté","es":"Makueni Condado","de":"Makueni Landkreis"}',
                'latitude' => '-2.25587340',
                'longitude' => '37.89366630',
            ),
            188 => 
            array (
                'id' => 189,
                'name' => 'Eastern Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '400',
                'translations' => '{"en":"Eastern Province","ar":"Eastern مقاطعة","fr":"Eastern Province","es":"Eastern Provincia","de":"Eastern Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            189 => 
            array (
                'id' => 190,
                'name' => 'Migori County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '27',
                'translations' => '{"en":"Migori County","ar":"Migori مقاطعة","fr":"Migori Comté","es":"Migori Condado","de":"Migori Landkreis"}',
                'latitude' => '-0.93657020',
                'longitude' => '34.41982430',
            ),
            190 => 
            array (
                'id' => 191,
                'name' => 'Nairobi',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '110',
                'translations' => '{"en":"Nairobi","ar":"Nairobi","fr":"Nairobi","es":"Nairobi","de":"Nairobi"}',
                'latitude' => '-1.29206590',
                'longitude' => '36.82194620',
            ),
            191 => 
            array (
                'id' => 192,
                'name' => 'Nyandarua County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '35',
                'translations' => '{"en":"Nyandarua County","ar":"Nyandarua مقاطعة","fr":"Nyandarua Comté","es":"Nyandarua Condado","de":"Nyandarua Landkreis"}',
                'latitude' => '-0.18038550',
                'longitude' => '36.52296410',
            ),
            192 => 
            array (
                'id' => 193,
                'name' => 'Kericho County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '12',
                'translations' => '{"en":"Kericho County","ar":"Kericho مقاطعة","fr":"Kericho Comté","es":"Kericho Condado","de":"Kericho Landkreis"}',
                'latitude' => '-0.18279130',
                'longitude' => '35.47819260',
            ),
            193 => 
            array (
                'id' => 194,
                'name' => 'Marsabit County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '25',
                'translations' => '{"en":"Marsabit County","ar":"Marsabit مقاطعة","fr":"Marsabit Comté","es":"Marsabit Condado","de":"Marsabit Landkreis"}',
                'latitude' => '2.44264030',
                'longitude' => '37.97845850',
            ),
            194 => 
            array (
                'id' => 195,
                'name' => 'Homa Bay County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '08',
                'translations' => '{"en":"Homa Bay County","ar":"Homa Bay مقاطعة","fr":"Homa Bay Comté","es":"Homa Bay Condado","de":"Homa Bay Landkreis"}',
                'latitude' => '-0.62206550',
                'longitude' => '34.33103640',
            ),
            195 => 
            array (
                'id' => 196,
                'name' => 'Garissa County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '07',
                'translations' => '{"en":"Garissa County","ar":"Garissa مقاطعة","fr":"Garissa Comté","es":"Garissa Condado","de":"Garissa Landkreis"}',
                'latitude' => '-0.45322930',
                'longitude' => '39.64609880',
            ),
            196 => 
            array (
                'id' => 197,
                'name' => 'Kajiado County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '10',
                'translations' => '{"en":"Kajiado County","ar":"Kajiado مقاطعة","fr":"Kajiado Comté","es":"Kajiado Condado","de":"Kajiado Landkreis"}',
                'latitude' => '-2.09807510',
                'longitude' => '36.78195050',
            ),
            197 => 
            array (
                'id' => 198,
                'name' => 'Meru County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '26',
                'translations' => '{"en":"Meru County","ar":"Meru مقاطعة","fr":"Meru Comté","es":"Meru Condado","de":"Meru Landkreis"}',
                'latitude' => '0.35571740',
                'longitude' => '37.80876930',
            ),
            198 => 
            array (
                'id' => 199,
                'name' => 'Kiambu County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '13',
                'translations' => '{"en":"Kiambu County","ar":"Kiambu مقاطعة","fr":"Kiambu Comté","es":"Kiambu Condado","de":"Kiambu Landkreis"}',
                'latitude' => '-1.03137010',
                'longitude' => '36.86807910',
            ),
            199 => 
            array (
                'id' => 200,
                'name' => 'Mombasa County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '28',
                'translations' => '{"en":"Mombasa County","ar":"Mombasa مقاطعة","fr":"Mombasa Comté","es":"Mombasa Condado","de":"Mombasa Landkreis"}',
                'latitude' => '-3.97682910',
                'longitude' => '39.71371810',
            ),
            200 => 
            array (
                'id' => 201,
                'name' => 'Elgeyo-Marakwet County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '05',
                'translations' => '{"en":"Elgeyo-Marakwet County","ar":"Elgeyo-Marakwet مقاطعة","fr":"Elgeyo-Marakwet Comté","es":"Elgeyo-Marakwet Condado","de":"Elgeyo-Marakwet Landkreis"}',
                'latitude' => '1.04982370',
                'longitude' => '35.47819260',
            ),
            201 => 
            array (
                'id' => 202,
                'name' => 'Vihiga District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '45',
                'translations' => '{"en":"Vihiga District","ar":"Vihiga مقاطعة","fr":"Vihiga District","es":"Vihiga Distrito","de":"Vihiga Bezirk"}',
                'latitude' => '0.07675530',
                'longitude' => '34.70776650',
            ),
            202 => 
            array (
                'id' => 203,
                'name' => 'Nakuru District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '31',
                'translations' => '{"en":"Nakuru District","ar":"Nakuru مقاطعة","fr":"Nakuru District","es":"Nakuru Distrito","de":"Nakuru Bezirk"}',
                'latitude' => '-0.30309880',
                'longitude' => '36.08002600',
            ),
            203 => 
            array (
                'id' => 204,
                'name' => 'Nyanza Province',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '600',
                'translations' => '{"en":"Nyanza Province","ar":"Nyanza مقاطعة","fr":"Nyanza Province","es":"Nyanza Provincia","de":"Nyanza Provinz"}',
                'latitude' => '-0.08898940',
                'longitude' => '34.77179120',
            ),
            204 => 
            array (
                'id' => 205,
                'name' => 'Tana River County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '40',
                'translations' => '{"en":"Tana River County","ar":"Tana River مقاطعة","fr":"Tana River Comté","es":"Tana River Condado","de":"Tana River Landkreis"}',
                'latitude' => '-1.65184680',
                'longitude' => '39.65181650',
            ),
            205 => 
            array (
                'id' => 206,
                'name' => 'Turkana County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '43',
                'translations' => '{"en":"Turkana County","ar":"Turkana مقاطعة","fr":"Turkana Comté","es":"Turkana Condado","de":"Turkana Landkreis"}',
                'latitude' => '3.31224770',
                'longitude' => '35.56578620',
            ),
            206 => 
            array (
                'id' => 207,
                'name' => 'Samburu County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '37',
                'translations' => '{"en":"Samburu County","ar":"Samburu مقاطعة","fr":"Samburu Comté","es":"Samburu Condado","de":"Samburu Landkreis"}',
                'latitude' => '1.21545060',
                'longitude' => '36.95410700',
            ),
            207 => 
            array (
                'id' => 208,
                'name' => 'West Pokot County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '47',
                'translations' => '{"en":"West Pokot County","ar":"West Pokot مقاطعة","fr":"West Pokot Comté","es":"West Pokot Condado","de":"West Pokot Landkreis"}',
                'latitude' => '1.62100760',
                'longitude' => '35.39050460',
            ),
            208 => 
            array (
                'id' => 209,
                'name' => 'Nyamira District',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '34',
                'translations' => '{"en":"Nyamira District","ar":"Nyamira مقاطعة","fr":"Nyamira District","es":"Nyamira Distrito","de":"Nyamira Bezirk"}',
                'latitude' => '-0.56694050',
                'longitude' => '34.93412340',
            ),
            209 => 
            array (
                'id' => 210,
                'name' => 'Bomet County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '02',
                'translations' => '{"en":"Bomet County","ar":"Bomet مقاطعة","fr":"Bomet Comté","es":"Bomet Condado","de":"Bomet Landkreis"}',
                'latitude' => '-0.80150090',
                'longitude' => '35.30272260',
            ),
            210 => 
            array (
                'id' => 211,
                'name' => 'Kitui County',
                'country_id' => 113,
                'country_code' => 'KE',
                'iso2' => '18',
                'translations' => '{"en":"Kitui County","ar":"Kitui مقاطعة","fr":"Kitui Comté","es":"Kitui Condado","de":"Kitui Landkreis"}',
                'latitude' => '-1.68328220',
                'longitude' => '38.31657250',
            ),
            211 => 
            array (
                'id' => 212,
                'name' => 'Bié Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'BIE',
                'translations' => '{"en":"Bié Province","ar":"Bié مقاطعة","fr":"Bié Province","es":"Bié Provincia","de":"Bié Provinz"}',
                'latitude' => '-12.57279070',
                'longitude' => '17.66888700',
            ),
            212 => 
            array (
                'id' => 213,
                'name' => 'Huambo Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'HUA',
                'translations' => '{"en":"Huambo Province","ar":"Huambo مقاطعة","fr":"Huambo Province","es":"Huambo Provincia","de":"Huambo Provinz"}',
                'latitude' => '-12.52682210',
                'longitude' => '15.59433880',
            ),
            213 => 
            array (
                'id' => 214,
                'name' => 'Zaire Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'ZAI',
                'translations' => '{"en":"Zaire Province","ar":"Zaire مقاطعة","fr":"Zaire Province","es":"Zaire Provincia","de":"Zaire Provinz"}',
                'latitude' => '-6.57334580',
                'longitude' => '13.17403480',
            ),
            214 => 
            array (
                'id' => 215,
                'name' => 'Cunene Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'CNN',
                'translations' => '{"en":"Cunene Province","ar":"Cunene مقاطعة","fr":"Cunene Province","es":"Cunene Provincia","de":"Cunene Provinz"}',
                'latitude' => '-16.28022210',
                'longitude' => '16.15809370',
            ),
            215 => 
            array (
                'id' => 216,
                'name' => 'Cuanza Sul',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'CUS',
                'translations' => '{"en":"Cuanza Sul","ar":"Cuanza Sul","fr":"Cuanza Sul","es":"Cuanza Sul","de":"Cuanza Sul"}',
                'latitude' => '-10.59519100',
                'longitude' => '15.40680790',
            ),
            216 => 
            array (
                'id' => 217,
                'name' => 'Cuanza Norte Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'CNO',
                'translations' => '{"en":"Cuanza Norte Province","ar":"Cuanza Norte مقاطعة","fr":"Cuanza Norte Province","es":"Cuanza Norte Provincia","de":"Cuanza Norte Provinz"}',
                'latitude' => '-9.23985130',
                'longitude' => '14.65878210',
            ),
            217 => 
            array (
                'id' => 218,
                'name' => 'Benguela Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'BGU',
                'translations' => '{"en":"Benguela Province","ar":"Benguela مقاطعة","fr":"Benguela Province","es":"Benguela Provincia","de":"Benguela Provinz"}',
                'latitude' => '-12.80037440',
                'longitude' => '13.91439900',
            ),
            218 => 
            array (
                'id' => 219,
                'name' => 'Moxico Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'MOX',
                'translations' => '{"en":"Moxico Province","ar":"Moxico مقاطعة","fr":"Moxico Province","es":"Moxico Provincia","de":"Moxico Provinz"}',
                'latitude' => '-13.42935790',
                'longitude' => '20.33088140',
            ),
            219 => 
            array (
                'id' => 220,
                'name' => 'Lunda Sul Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'LSU',
                'translations' => '{"en":"Lunda Sul Province","ar":"Lunda Sul مقاطعة","fr":"Lunda Sul Province","es":"Lunda Sul Provincia","de":"Lunda Sul Provinz"}',
                'latitude' => '-10.28665780',
                'longitude' => '20.71224650',
            ),
            220 => 
            array (
                'id' => 221,
                'name' => 'Bengo Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'BGO',
                'translations' => '{"en":"Bengo Province","ar":"Bengo مقاطعة","fr":"Bengo Province","es":"Bengo Provincia","de":"Bengo Provinz"}',
                'latitude' => '-9.10422570',
                'longitude' => '13.72891670',
            ),
            221 => 
            array (
                'id' => 222,
                'name' => 'Luanda Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'LUA',
                'translations' => '{"en":"Luanda Province","ar":"Luanda مقاطعة","fr":"Luanda Province","es":"Luanda Provincia","de":"Luanda Provinz"}',
                'latitude' => '-9.03508800',
                'longitude' => '13.26634790',
            ),
            222 => 
            array (
                'id' => 223,
                'name' => 'Lunda Norte Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'LNO',
                'translations' => '{"en":"Lunda Norte Province","ar":"Lunda Norte مقاطعة","fr":"Lunda Norte Province","es":"Lunda Norte Provincia","de":"Lunda Norte Provinz"}',
                'latitude' => '-8.35250220',
                'longitude' => '19.18800470',
            ),
            223 => 
            array (
                'id' => 224,
                'name' => 'Uíge Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'UIG',
                'translations' => '{"en":"Uíge Province","ar":"Uíge مقاطعة","fr":"Uíge Province","es":"Uíge Provincia","de":"Uíge Provinz"}',
                'latitude' => '-7.17367320',
                'longitude' => '15.40680790',
            ),
            224 => 
            array (
                'id' => 225,
                'name' => 'Huíla Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'HUI',
                'translations' => '{"en":"Huíla Province","ar":"Huíla مقاطعة","fr":"Huíla Province","es":"Huíla Provincia","de":"Huíla Provinz"}',
                'latitude' => '-14.92805530',
                'longitude' => '14.65878210',
            ),
            225 => 
            array (
                'id' => 226,
                'name' => 'Cuando Cubango Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'CCU',
                'translations' => '{"en":"Cuando Cubango Province","ar":"Cuando Cubango مقاطعة","fr":"Cuando Cubango Province","es":"Cuando Cubango Provincia","de":"Cuando Cubango Provinz"}',
                'latitude' => '-16.41808240',
                'longitude' => '18.80761950',
            ),
            226 => 
            array (
                'id' => 227,
                'name' => 'Malanje Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'MAL',
                'translations' => '{"en":"Malanje Province","ar":"Malanje مقاطعة","fr":"Malanje Province","es":"Malanje Provincia","de":"Malanje Provinz"}',
                'latitude' => '-9.82511830',
                'longitude' => '16.91225100',
            ),
            227 => 
            array (
                'id' => 228,
                'name' => 'Cabinda Province',
                'country_id' => 7,
                'country_code' => 'AO',
                'iso2' => 'CAB',
                'translations' => '{"en":"Cabinda Province","ar":"Cabinda مقاطعة","fr":"Cabinda Province","es":"Cabinda Provincia","de":"Cabinda Provinz"}',
                'latitude' => '-5.02487490',
                'longitude' => '12.34638750',
            ),
            228 => 
            array (
                'id' => 229,
                'name' => 'Gasa District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => 'GA',
                'translations' => '{"en":"Gasa District","ar":"Gasa مقاطعة","fr":"Gasa District","es":"Gasa Distrito","de":"Gasa Bezirk"}',
                'latitude' => '28.01858860',
                'longitude' => '89.92532330',
            ),
            229 => 
            array (
                'id' => 230,
                'name' => 'Tsirang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '21',
                'translations' => '{"en":"Tsirang District","ar":"Tsirang مقاطعة","fr":"Tsirang District","es":"Tsirang Distrito","de":"Tsirang Bezirk"}',
                'latitude' => '27.03220700',
                'longitude' => '90.18696440',
            ),
            230 => 
            array (
                'id' => 231,
                'name' => 'Wangdue Phodrang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '24',
                'translations' => '{"en":"Wangdue Phodrang District","ar":"Wangdue Phodrang مقاطعة","fr":"Wangdue Phodrang District","es":"Wangdue Phodrang Distrito","de":"Wangdue Phodrang Bezirk"}',
                'latitude' => '27.45260460',
                'longitude' => '90.06749280',
            ),
            231 => 
            array (
                'id' => 232,
                'name' => 'Haa District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '13',
                'translations' => '{"en":"Haa District","ar":"Haa مقاطعة","fr":"Haa District","es":"Haa Distrito","de":"Haa Bezirk"}',
                'latitude' => '27.26516690',
                'longitude' => '89.17059980',
            ),
            232 => 
            array (
                'id' => 233,
                'name' => 'Zhemgang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '34',
                'translations' => '{"en":"Zhemgang District","ar":"Zhemgang مقاطعة","fr":"Zhemgang District","es":"Zhemgang Distrito","de":"Zhemgang Bezirk"}',
                'latitude' => '27.07697500',
                'longitude' => '90.82940020',
            ),
            233 => 
            array (
                'id' => 234,
                'name' => 'Lhuntse District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '44',
                'translations' => '{"en":"Lhuntse District","ar":"Lhuntse مقاطعة","fr":"Lhuntse District","es":"Lhuntse Distrito","de":"Lhuntse Bezirk"}',
                'latitude' => '27.82649890',
                'longitude' => '91.13530200',
            ),
            234 => 
            array (
                'id' => 235,
                'name' => 'Punakha District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '23',
                'translations' => '{"en":"Punakha District","ar":"Punakha مقاطعة","fr":"Punakha District","es":"Punakha Distrito","de":"Punakha Bezirk"}',
                'latitude' => '27.69037160',
                'longitude' => '89.88793040',
            ),
            235 => 
            array (
                'id' => 236,
                'name' => 'Trashigang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '41',
                'translations' => '{"en":"Trashigang District","ar":"Trashigang مقاطعة","fr":"Trashigang District","es":"Trashigang Distrito","de":"Trashigang Bezirk"}',
                'latitude' => '27.25667950',
                'longitude' => '91.75388170',
            ),
            236 => 
            array (
                'id' => 237,
                'name' => 'Paro District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '11',
                'translations' => '{"en":"Paro District","ar":"Paro مقاطعة","fr":"Paro District","es":"Paro Distrito","de":"Paro Bezirk"}',
                'latitude' => '27.42859490',
                'longitude' => '89.41665160',
            ),
            237 => 
            array (
                'id' => 238,
                'name' => 'Dagana District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '22',
                'translations' => '{"en":"Dagana District","ar":"Dagana مقاطعة","fr":"Dagana District","es":"Dagana Distrito","de":"Dagana Bezirk"}',
                'latitude' => '27.03228610',
                'longitude' => '89.88793040',
            ),
            238 => 
            array (
                'id' => 239,
                'name' => 'Chukha District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '12',
                'translations' => '{"en":"Chukha District","ar":"Chukha مقاطعة","fr":"Chukha District","es":"Chukha Distrito","de":"Chukha Bezirk"}',
                'latitude' => '27.07843040',
                'longitude' => '89.47421770',
            ),
            239 => 
            array (
                'id' => 240,
                'name' => 'Bumthang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '33',
                'translations' => '{"en":"Bumthang District","ar":"Bumthang مقاطعة","fr":"Bumthang District","es":"Bumthang Distrito","de":"Bumthang Bezirk"}',
                'latitude' => '27.64183900',
                'longitude' => '90.67730460',
            ),
            240 => 
            array (
                'id' => 241,
                'name' => 'Thimphu District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '15',
                'translations' => '{"en":"Thimphu District","ar":"Thimphu مقاطعة","fr":"Thimphu District","es":"Thimphu Distrito","de":"Thimphu Bezirk"}',
                'latitude' => '27.47122160',
                'longitude' => '89.63390410',
            ),
            241 => 
            array (
                'id' => 242,
                'name' => 'Mongar District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '42',
                'translations' => '{"en":"Mongar District","ar":"Mongar مقاطعة","fr":"Mongar District","es":"Mongar Distrito","de":"Mongar Bezirk"}',
                'latitude' => '27.26170590',
                'longitude' => '91.28910360',
            ),
            242 => 
            array (
                'id' => 243,
                'name' => 'Samdrup Jongkhar District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '45',
                'translations' => '{"en":"Samdrup Jongkhar District","ar":"Samdrup Jongkhar مقاطعة","fr":"Samdrup Jongkhar District","es":"Samdrup Jongkhar Distrito","de":"Samdrup Jongkhar Bezirk"}',
                'latitude' => '26.80356820',
                'longitude' => '91.50392070',
            ),
            243 => 
            array (
                'id' => 244,
                'name' => 'Pemagatshel District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '43',
                'translations' => '{"en":"Pemagatshel District","ar":"Pemagatshel مقاطعة","fr":"Pemagatshel District","es":"Pemagatshel Distrito","de":"Pemagatshel Bezirk"}',
                'latitude' => '27.00238200',
                'longitude' => '91.34692470',
            ),
            244 => 
            array (
                'id' => 245,
                'name' => 'Trongsa District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '32',
                'translations' => '{"en":"Trongsa District","ar":"Trongsa مقاطعة","fr":"Trongsa District","es":"Trongsa Distrito","de":"Trongsa Bezirk"}',
                'latitude' => '27.50022690',
                'longitude' => '90.50806340',
            ),
            245 => 
            array (
                'id' => 246,
                'name' => 'Samtse District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '14',
                'translations' => '{"en":"Samtse District","ar":"Samtse مقاطعة","fr":"Samtse District","es":"Samtse Distrito","de":"Samtse Bezirk"}',
                'latitude' => '27.02918320',
                'longitude' => '89.05615320',
            ),
            246 => 
            array (
                'id' => 247,
                'name' => 'Sarpang District',
                'country_id' => 26,
                'country_code' => 'BT',
                'iso2' => '31',
                'translations' => '{"en":"Sarpang District","ar":"Sarpang مقاطعة","fr":"Sarpang District","es":"Sarpang Distrito","de":"Sarpang Bezirk"}',
                'latitude' => '26.93730410',
                'longitude' => '90.48799160',
            ),
            247 => 
            array (
                'id' => 248,
                'name' => 'Tombouctou Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '6',
                'translations' => '{"en":"Tombouctou Region","ar":"Tombouctou منطقة","fr":"Tombouctou Région","es":"Tombouctou Región","de":"Tombouctou Region"}',
                'latitude' => '21.05267060',
                'longitude' => '-3.74350900',
            ),
            248 => 
            array (
                'id' => 249,
                'name' => 'Ségou Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '4',
                'translations' => '{"en":"Ségou Region","ar":"Ségou منطقة","fr":"Ségou Région","es":"Ségou Región","de":"Ségou Region"}',
                'latitude' => '13.83944560',
                'longitude' => '-6.06791940',
            ),
            249 => 
            array (
                'id' => 250,
                'name' => 'Koulikoro Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '2',
                'translations' => '{"en":"Koulikoro Region","ar":"Koulikoro منطقة","fr":"Koulikoro Région","es":"Koulikoro Región","de":"Koulikoro Region"}',
                'latitude' => '13.80180740',
                'longitude' => '-7.43813550',
            ),
            250 => 
            array (
                'id' => 251,
                'name' => 'Ménaka Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '9',
                'translations' => '{"en":"Ménaka Region","ar":"Ménaka منطقة","fr":"Ménaka Région","es":"Ménaka Región","de":"Ménaka Region"}',
                'latitude' => '15.91564210',
                'longitude' => '2.39617400',
            ),
            251 => 
            array (
                'id' => 252,
                'name' => 'Kayes Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '1',
                'translations' => '{"en":"Kayes Region","ar":"Kayes منطقة","fr":"Kayes Région","es":"Kayes Región","de":"Kayes Region"}',
                'latitude' => '14.08183080',
                'longitude' => '-9.90181310',
            ),
            252 => 
            array (
                'id' => 253,
                'name' => 'Bamako',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => 'BKO',
                'translations' => '{"en":"Bamako","ar":"Bamako","fr":"Bamako","es":"Bamako","de":"Bamako"}',
                'latitude' => '12.63923160',
                'longitude' => '-8.00288920',
            ),
            253 => 
            array (
                'id' => 254,
                'name' => 'Sikasso Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '3',
                'translations' => '{"en":"Sikasso Region","ar":"Sikasso منطقة","fr":"Sikasso Région","es":"Sikasso Región","de":"Sikasso Region"}',
                'latitude' => '10.89051860',
                'longitude' => '-7.43813550',
            ),
            254 => 
            array (
                'id' => 255,
                'name' => 'Mopti Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '5',
                'translations' => '{"en":"Mopti Region","ar":"Mopti منطقة","fr":"Mopti Région","es":"Mopti Región","de":"Mopti Region"}',
                'latitude' => '14.63380390',
                'longitude' => '-3.41955270',
            ),
            255 => 
            array (
                'id' => 256,
                'name' => 'Taoudénit Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '10',
                'translations' => '{"en":"Taoudénit Region","ar":"Taoudénit منطقة","fr":"Taoudénit Région","es":"Taoudénit Región","de":"Taoudénit Region"}',
                'latitude' => '22.67641320',
                'longitude' => '-3.97891430',
            ),
            256 => 
            array (
                'id' => 257,
                'name' => 'Kidal Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '8',
                'translations' => '{"en":"Kidal Region","ar":"Kidal منطقة","fr":"Kidal Région","es":"Kidal Región","de":"Kidal Region"}',
                'latitude' => '18.79868320',
                'longitude' => '1.83183340',
            ),
            257 => 
            array (
                'id' => 258,
                'name' => 'Gao Region',
                'country_id' => 134,
                'country_code' => 'ML',
                'iso2' => '7',
                'translations' => '{"en":"Gao Region","ar":"Gao منطقة","fr":"Gao Région","es":"Gao Región","de":"Gao Region"}',
                'latitude' => '16.90663320',
                'longitude' => '1.52086240',
            ),
            258 => 
            array (
                'id' => 259,
                'name' => 'Southern Province',
                'country_id' => 183,
                'country_code' => 'RW',
                'iso2' => '05',
                'translations' => '{"en":"Southern Province","ar":"Southern مقاطعة","fr":"Southern Province","es":"Southern Provincia","de":"Southern Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            259 => 
            array (
                'id' => 260,
                'name' => 'Western Province',
                'country_id' => 183,
                'country_code' => 'RW',
                'iso2' => '04',
                'translations' => '{"en":"Western Province","ar":"Western مقاطعة","fr":"Western Province","es":"Western Provincia","de":"Western Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            260 => 
            array (
                'id' => 261,
                'name' => 'Eastern Province',
                'country_id' => 183,
                'country_code' => 'RW',
                'iso2' => '02',
                'translations' => '{"en":"Eastern Province","ar":"Eastern مقاطعة","fr":"Eastern Province","es":"Eastern Provincia","de":"Eastern Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            261 => 
            array (
                'id' => 262,
                'name' => 'Kigali district',
                'country_id' => 183,
                'country_code' => 'RW',
                'iso2' => '01',
                'translations' => '{"en":"Kigali district","ar":"Kigali district","fr":"Kigali district","es":"Kigali district","de":"Kigali district"}',
                'latitude' => '-1.94407270',
                'longitude' => '30.06188510',
            ),
            262 => 
            array (
                'id' => 263,
                'name' => 'Northern Province',
                'country_id' => 183,
                'country_code' => 'RW',
                'iso2' => '03',
                'translations' => '{"en":"Northern Province","ar":"Northern مقاطعة","fr":"Northern Province","es":"Northern Provincia","de":"Northern Provinz"}',
                'latitude' => NULL,
                'longitude' => NULL,
            ),
            263 => 
            array (
                'id' => 264,
                'name' => 'Belize District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'BZ',
                'translations' => '{"en":"Belize District","ar":"Belize مقاطعة","fr":"Belize District","es":"Belize Distrito","de":"Belize Bezirk"}',
                'latitude' => '17.56776790',
                'longitude' => '-88.40160410',
            ),
            264 => 
            array (
                'id' => 265,
                'name' => 'Stann Creek District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'SC',
                'translations' => '{"en":"Stann Creek District","ar":"Stann Creek مقاطعة","fr":"Stann Creek District","es":"Stann Creek Distrito","de":"Stann Creek Bezirk"}',
                'latitude' => '16.81166310',
                'longitude' => '-88.40160410',
            ),
            265 => 
            array (
                'id' => 266,
                'name' => 'Corozal District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'CZL',
                'translations' => '{"en":"Corozal District","ar":"Corozal مقاطعة","fr":"Corozal District","es":"Corozal Distrito","de":"Corozal Bezirk"}',
                'latitude' => '18.13492380',
                'longitude' => '-88.24611830',
            ),
            266 => 
            array (
                'id' => 267,
                'name' => 'Toledo District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'TOL',
                'translations' => '{"en":"Toledo District","ar":"Toledo مقاطعة","fr":"Toledo District","es":"Toledo Distrito","de":"Toledo Bezirk"}',
                'latitude' => '16.24919230',
                'longitude' => '-88.86469800',
            ),
            267 => 
            array (
                'id' => 268,
                'name' => 'Orange Walk District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'OW',
                'translations' => '{"en":"Orange Walk District","ar":"Orange Walk مقاطعة","fr":"Orange Walk District","es":"Orange Walk Distrito","de":"Orange Walk Bezirk"}',
                'latitude' => '17.76035300',
                'longitude' => '-88.86469800',
            ),
            268 => 
            array (
                'id' => 269,
                'name' => 'Cayo District',
                'country_id' => 23,
                'country_code' => 'BZ',
                'iso2' => 'CY',
                'translations' => '{"en":"Cayo District","ar":"Cayo مقاطعة","fr":"Cayo District","es":"Cayo Distrito","de":"Cayo Bezirk"}',
                'latitude' => '17.09844450',
                'longitude' => '-88.94138650',
            ),
            269 => 
            array (
                'id' => 270,
                'name' => 'Príncipe Province',
                'country_id' => 193,
                'country_code' => 'ST',
                'iso2' => 'P',
                'translations' => '{"en":"Príncipe Province","ar":"Príncipe مقاطعة","fr":"Príncipe Province","es":"Príncipe Provincia","de":"Príncipe Provinz"}',
                'latitude' => '1.61393810',
                'longitude' => '7.40569280',
            ),
            270 => 
            array (
                'id' => 271,
                'name' => 'São Tomé Province',
                'country_id' => 193,
                'country_code' => 'ST',
                'iso2' => 'S',
                'translations' => '{"en":"São Tomé Province","ar":"São Tomé مقاطعة","fr":"São Tomé Province","es":"São Tomé Provincia","de":"São Tomé Provinz"}',
                'latitude' => '0.33019240',
                'longitude' => '6.73334300',
            ),
            271 => 
            array (
                'id' => 272,
                'name' => 'Havana Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '03',
                'translations' => '{"en":"Havana Province","ar":"Havana مقاطعة","fr":"Havana Province","es":"Havana Provincia","de":"Havana Provinz"}',
                'latitude' => '23.05406980',
                'longitude' => '-82.34518900',
            ),
            272 => 
            array (
                'id' => 273,
                'name' => 'Santiago de Cuba Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '13',
                'translations' => '{"en":"Santiago de Cuba Province","ar":"Santiago de Cuba مقاطعة","fr":"Santiago de Cuba Province","es":"Santiago de Cuba Provincia","de":"Santiago de Cuba Provinz"}',
                'latitude' => '20.23976820',
                'longitude' => '-75.99276520',
            ),
            273 => 
            array (
                'id' => 274,
                'name' => 'Sancti Spíritus Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '07',
                'translations' => '{"en":"Sancti Spíritus Province","ar":"Sancti Spíritus مقاطعة","fr":"Sancti Spíritus Province","es":"Sancti Spíritus Provincia","de":"Sancti Spíritus Provinz"}',
                'latitude' => '21.99382140',
                'longitude' => '-79.47038850',
            ),
            274 => 
            array (
                'id' => 275,
                'name' => 'Granma Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '12',
                'translations' => '{"en":"Granma Province","ar":"Granma مقاطعة","fr":"Granma Province","es":"Granma Provincia","de":"Granma Provinz"}',
                'latitude' => '20.38449020',
                'longitude' => '-76.64127120',
            ),
            275 => 
            array (
                'id' => 276,
                'name' => 'Mayabeque Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '16',
                'translations' => '{"en":"Mayabeque Province","ar":"Mayabeque مقاطعة","fr":"Mayabeque Province","es":"Mayabeque Provincia","de":"Mayabeque Provinz"}',
                'latitude' => '22.89265290',
                'longitude' => '-81.95348150',
            ),
            276 => 
            array (
                'id' => 277,
                'name' => 'Pinar del Río Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '01',
                'translations' => '{"en":"Pinar del Río Province","ar":"Pinar del Río مقاطعة","fr":"Pinar del Río Province","es":"Pinar del Río Provincia","de":"Pinar del Río Provinz"}',
                'latitude' => '22.40762560',
                'longitude' => '-83.84730150',
            ),
            277 => 
            array (
                'id' => 278,
                'name' => 'Isla de la Juventud',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '99',
                'translations' => '{"en":"Isla de la Juventud","ar":"Isla de la Juventud","fr":"Isla de la Juventud","es":"Isla de la Juventud","de":"Isla de la Juventud"}',
                'latitude' => '21.70847370',
                'longitude' => '-82.82202320',
            ),
            278 => 
            array (
                'id' => 279,
                'name' => 'Holguín Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '11',
                'translations' => '{"en":"Holguín Province","ar":"Holguín مقاطعة","fr":"Holguín Province","es":"Holguín Provincia","de":"Holguín Provinz"}',
                'latitude' => '20.78378930',
                'longitude' => '-75.80690820',
            ),
            279 => 
            array (
                'id' => 280,
                'name' => 'Villa Clara Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '05',
                'translations' => '{"en":"Villa Clara Province","ar":"Villa Clara مقاطعة","fr":"Villa Clara Province","es":"Villa Clara Provincia","de":"Villa Clara Provinz"}',
                'latitude' => '22.49372040',
                'longitude' => '-79.91927020',
            ),
            280 => 
            array (
                'id' => 281,
                'name' => 'Las Tunas Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '10',
                'translations' => '{"en":"Las Tunas Province","ar":"Las Tunas مقاطعة","fr":"Las Tunas Province","es":"Las Tunas Provincia","de":"Las Tunas Provinz"}',
                'latitude' => '21.06051620',
                'longitude' => '-76.91820970',
            ),
            281 => 
            array (
                'id' => 282,
                'name' => 'Ciego de Ávila Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '08',
                'translations' => '{"en":"Ciego de Ávila Province","ar":"Ciego de Ávila مقاطعة","fr":"Ciego de Ávila Province","es":"Ciego de Ávila Provincia","de":"Ciego de Ávila Provinz"}',
                'latitude' => '21.93295150',
                'longitude' => '-78.56608520',
            ),
            282 => 
            array (
                'id' => 283,
                'name' => 'Artemisa Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '15',
                'translations' => '{"en":"Artemisa Province","ar":"Artemisa مقاطعة","fr":"Artemisa Province","es":"Artemisa Provincia","de":"Artemisa Provinz"}',
                'latitude' => '22.75229030',
                'longitude' => '-82.99316070',
            ),
            283 => 
            array (
                'id' => 284,
                'name' => 'Matanzas Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '04',
                'translations' => '{"en":"Matanzas Province","ar":"Matanzas مقاطعة","fr":"Matanzas Province","es":"Matanzas Provincia","de":"Matanzas Provinz"}',
                'latitude' => '22.57671230',
                'longitude' => '-81.33994140',
            ),
            284 => 
            array (
                'id' => 285,
                'name' => 'Guantánamo Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '14',
                'translations' => '{"en":"Guantánamo Province","ar":"Guantánamo مقاطعة","fr":"Guantánamo Province","es":"Guantánamo Provincia","de":"Guantánamo Provinz"}',
                'latitude' => '20.14559170',
                'longitude' => '-74.87410450',
            ),
            285 => 
            array (
                'id' => 286,
                'name' => 'Camagüey Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '09',
                'translations' => '{"en":"Camagüey Province","ar":"Camagüey مقاطعة","fr":"Camagüey Province","es":"Camagüey Provincia","de":"Camagüey Provinz"}',
                'latitude' => '21.21672470',
                'longitude' => '-77.74520810',
            ),
            286 => 
            array (
                'id' => 287,
                'name' => 'Cienfuegos Province',
                'country_id' => 56,
                'country_code' => 'CU',
                'iso2' => '06',
                'translations' => '{"en":"Cienfuegos Province","ar":"Cienfuegos مقاطعة","fr":"Cienfuegos Province","es":"Cienfuegos Provincia","de":"Cienfuegos Provinz"}',
                'latitude' => '22.23797830',
                'longitude' => '-80.36586500',
            ),
            287 => 
            array (
                'id' => 288,
                'name' => 'Jigawa State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'JI',
                'translations' => '{"en":"Jigawa State","ar":"Jigawa ولاية","fr":"Jigawa État","es":"Jigawa Estado","de":"Jigawa Staat"}',
                'latitude' => '12.22801200',
                'longitude' => '9.56158670',
            ),
            288 => 
            array (
                'id' => 289,
                'name' => 'Enugu State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'EN',
                'translations' => '{"en":"Enugu State","ar":"Enugu ولاية","fr":"Enugu État","es":"Enugu Estado","de":"Enugu Staat"}',
                'latitude' => '6.53635300',
                'longitude' => '7.43561940',
            ),
            289 => 
            array (
                'id' => 290,
                'name' => 'Kebbi State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KE',
                'translations' => '{"en":"Kebbi State","ar":"Kebbi ولاية","fr":"Kebbi État","es":"Kebbi Estado","de":"Kebbi Staat"}',
                'latitude' => '11.49420030',
                'longitude' => '4.23333550',
            ),
            290 => 
            array (
                'id' => 291,
                'name' => 'Benue State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'BE',
                'translations' => '{"en":"Benue State","ar":"Benue ولاية","fr":"Benue État","es":"Benue Estado","de":"Benue Staat"}',
                'latitude' => '7.33690240',
                'longitude' => '8.74036870',
            ),
            291 => 
            array (
                'id' => 292,
                'name' => 'Sokoto State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'SO',
                'translations' => '{"en":"Sokoto State","ar":"Sokoto ولاية","fr":"Sokoto État","es":"Sokoto Estado","de":"Sokoto Staat"}',
                'latitude' => '13.05331430',
                'longitude' => '5.32227220',
            ),
            292 => 
            array (
                'id' => 293,
                'name' => 'Federal Capital Territory',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'FC',
                'translations' => '{"en":"Federal Capital Territory","ar":"Federal Capital إقليم","fr":"Federal Capital Territoire","es":"Federal Capital Territorio","de":"Federal Capital Territorium"}',
                'latitude' => '8.89406910',
                'longitude' => '7.18604020',
            ),
            293 => 
            array (
                'id' => 294,
                'name' => 'Kaduna State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KD',
                'translations' => '{"en":"Kaduna State","ar":"Kaduna ولاية","fr":"Kaduna État","es":"Kaduna Estado","de":"Kaduna Staat"}',
                'latitude' => '10.37640060',
                'longitude' => '7.70945370',
            ),
            294 => 
            array (
                'id' => 295,
                'name' => 'Kwara State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KW',
                'translations' => '{"en":"Kwara State","ar":"Kwara ولاية","fr":"Kwara État","es":"Kwara Estado","de":"Kwara Staat"}',
                'latitude' => '8.96689610',
                'longitude' => '4.38740510',
            ),
            295 => 
            array (
                'id' => 296,
                'name' => 'Oyo State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'OY',
                'translations' => '{"en":"Oyo State","ar":"Oyo ولاية","fr":"Oyo État","es":"Oyo Estado","de":"Oyo Staat"}',
                'latitude' => '8.15738090',
                'longitude' => '3.61465340',
            ),
            296 => 
            array (
                'id' => 297,
                'name' => 'Yobe State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'YO',
                'translations' => '{"en":"Yobe State","ar":"Yobe ولاية","fr":"Yobe État","es":"Yobe Estado","de":"Yobe Staat"}',
                'latitude' => '12.29387600',
                'longitude' => '11.43904110',
            ),
            297 => 
            array (
                'id' => 298,
                'name' => 'Kogi State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KO',
                'translations' => '{"en":"Kogi State","ar":"Kogi ولاية","fr":"Kogi État","es":"Kogi Estado","de":"Kogi Staat"}',
                'latitude' => '7.73373250',
                'longitude' => '6.69058360',
            ),
            298 => 
            array (
                'id' => 299,
                'name' => 'Zamfara State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'ZA',
                'translations' => '{"en":"Zamfara State","ar":"Zamfara ولاية","fr":"Zamfara État","es":"Zamfara Estado","de":"Zamfara Staat"}',
                'latitude' => '12.12218050',
                'longitude' => '6.22358190',
            ),
            299 => 
            array (
                'id' => 300,
                'name' => 'Kano State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KN',
                'translations' => '{"en":"Kano State","ar":"Kano ولاية","fr":"Kano État","es":"Kano Estado","de":"Kano Staat"}',
                'latitude' => '11.74706980',
                'longitude' => '8.52471070',
            ),
            300 => 
            array (
                'id' => 301,
                'name' => 'Nasarawa State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'NA',
                'translations' => '{"en":"Nasarawa State","ar":"Nasarawa ولاية","fr":"Nasarawa État","es":"Nasarawa Estado","de":"Nasarawa Staat"}',
                'latitude' => '8.49979080',
                'longitude' => '8.19969370',
            ),
            301 => 
            array (
                'id' => 302,
                'name' => 'Plateau State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'PL',
                'translations' => '{"en":"Plateau State","ar":"Plateau ولاية","fr":"Plateau État","es":"Plateau Estado","de":"Plateau Staat"}',
                'latitude' => '9.21820930',
                'longitude' => '9.51794880',
            ),
            302 => 
            array (
                'id' => 303,
                'name' => 'Abia State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'AB',
                'translations' => '{"en":"Abia State","ar":"Abia ولاية","fr":"Abia État","es":"Abia Estado","de":"Abia Staat"}',
                'latitude' => '5.45273540',
                'longitude' => '7.52484140',
            ),
            303 => 
            array (
                'id' => 304,
                'name' => 'Akwa Ibom State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'AK',
                'translations' => '{"en":"Akwa Ibom State","ar":"Akwa Ibom ولاية","fr":"Akwa Ibom État","es":"Akwa Ibom Estado","de":"Akwa Ibom Staat"}',
                'latitude' => '4.90573710',
                'longitude' => '7.85366750',
            ),
            304 => 
            array (
                'id' => 305,
                'name' => 'Bayelsa State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'BY',
                'translations' => '{"en":"Bayelsa State","ar":"Bayelsa ولاية","fr":"Bayelsa État","es":"Bayelsa Estado","de":"Bayelsa Staat"}',
                'latitude' => '4.77190710',
                'longitude' => '6.06985260',
            ),
            305 => 
            array (
                'id' => 306,
                'name' => 'Lagos',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'LA',
                'translations' => '{"en":"Lagos","ar":"Lagos","fr":"Lagos","es":"Lagos","de":"Lagos"}',
                'latitude' => '6.52437930',
                'longitude' => '3.37920570',
            ),
            306 => 
            array (
                'id' => 307,
                'name' => 'Borno State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'BO',
                'translations' => '{"en":"Borno State","ar":"Borno ولاية","fr":"Borno État","es":"Borno Estado","de":"Borno Staat"}',
                'latitude' => '11.88463560',
                'longitude' => '13.15196650',
            ),
            307 => 
            array (
                'id' => 308,
                'name' => 'Imo State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'IM',
                'translations' => '{"en":"Imo State","ar":"Imo ولاية","fr":"Imo État","es":"Imo Estado","de":"Imo Staat"}',
                'latitude' => '5.57201220',
                'longitude' => '7.05882190',
            ),
            308 => 
            array (
                'id' => 309,
                'name' => 'Ekiti State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'EK',
                'translations' => '{"en":"Ekiti State","ar":"Ekiti ولاية","fr":"Ekiti État","es":"Ekiti Estado","de":"Ekiti Staat"}',
                'latitude' => '7.71898620',
                'longitude' => '5.31095050',
            ),
            309 => 
            array (
                'id' => 310,
                'name' => 'Gombe State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'GO',
                'translations' => '{"en":"Gombe State","ar":"Gombe ولاية","fr":"Gombe État","es":"Gombe Estado","de":"Gombe Staat"}',
                'latitude' => '10.36377950',
                'longitude' => '11.19275870',
            ),
            310 => 
            array (
                'id' => 311,
                'name' => 'Ebonyi State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'EB',
                'translations' => '{"en":"Ebonyi State","ar":"Ebonyi ولاية","fr":"Ebonyi État","es":"Ebonyi Estado","de":"Ebonyi Staat"}',
                'latitude' => '6.26492320',
                'longitude' => '8.01373020',
            ),
            311 => 
            array (
                'id' => 312,
                'name' => 'Bauchi State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'BA',
                'translations' => '{"en":"Bauchi State","ar":"Bauchi ولاية","fr":"Bauchi État","es":"Bauchi Estado","de":"Bauchi Staat"}',
                'latitude' => '10.77606240',
                'longitude' => '9.99919430',
            ),
            312 => 
            array (
                'id' => 313,
                'name' => 'Katsina State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'KT',
                'translations' => '{"en":"Katsina State","ar":"Katsina ولاية","fr":"Katsina État","es":"Katsina Estado","de":"Katsina Staat"}',
                'latitude' => '12.37967070',
                'longitude' => '7.63057480',
            ),
            313 => 
            array (
                'id' => 314,
                'name' => 'Cross River State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'CR',
                'translations' => '{"en":"Cross River State","ar":"Cross River ولاية","fr":"Cross River État","es":"Cross River Estado","de":"Cross River Staat"}',
                'latitude' => '5.87017240',
                'longitude' => '8.59880140',
            ),
            314 => 
            array (
                'id' => 315,
                'name' => 'Anambra State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'AN',
                'translations' => '{"en":"Anambra State","ar":"Anambra ولاية","fr":"Anambra État","es":"Anambra Estado","de":"Anambra Staat"}',
                'latitude' => '6.22089970',
                'longitude' => '6.93695590',
            ),
            315 => 
            array (
                'id' => 316,
                'name' => 'Delta State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'DE',
                'translations' => '{"en":"Delta State","ar":"Delta ولاية","fr":"Delta État","es":"Delta Estado","de":"Delta Staat"}',
                'latitude' => '33.74537840',
                'longitude' => '-90.73545080',
            ),
            316 => 
            array (
                'id' => 317,
                'name' => 'Niger State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'NI',
                'translations' => '{"en":"Niger State","ar":"Niger ولاية","fr":"Niger État","es":"Niger Estado","de":"Niger Staat"}',
                'latitude' => '9.93092240',
                'longitude' => '5.59832100',
            ),
            317 => 
            array (
                'id' => 318,
                'name' => 'Edo State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'ED',
                'translations' => '{"en":"Edo State","ar":"Edo ولاية","fr":"Edo État","es":"Edo Estado","de":"Edo Staat"}',
                'latitude' => '6.63418310',
                'longitude' => '5.93040560',
            ),
            318 => 
            array (
                'id' => 319,
                'name' => 'Taraba State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'TA',
                'translations' => '{"en":"Taraba State","ar":"Taraba ولاية","fr":"Taraba État","es":"Taraba Estado","de":"Taraba Staat"}',
                'latitude' => '7.99936160',
                'longitude' => '10.77398630',
            ),
            319 => 
            array (
                'id' => 320,
                'name' => 'Adamawa State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'AD',
                'translations' => '{"en":"Adamawa State","ar":"Adamawa ولاية","fr":"Adamawa État","es":"Adamawa Estado","de":"Adamawa Staat"}',
                'latitude' => '9.32647510',
                'longitude' => '12.39838530',
            ),
            320 => 
            array (
                'id' => 321,
                'name' => 'Ondo State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'ON',
                'translations' => '{"en":"Ondo State","ar":"Ondo ولاية","fr":"Ondo État","es":"Ondo Estado","de":"Ondo Staat"}',
                'latitude' => '6.91486820',
                'longitude' => '5.14781440',
            ),
            321 => 
            array (
                'id' => 322,
                'name' => 'Osun State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'OS',
                'translations' => '{"en":"Osun State","ar":"Osun ولاية","fr":"Osun État","es":"Osun Estado","de":"Osun Staat"}',
                'latitude' => '7.56289640',
                'longitude' => '4.51995930',
            ),
            322 => 
            array (
                'id' => 323,
                'name' => 'Ogun State',
                'country_id' => 161,
                'country_code' => 'NG',
                'iso2' => 'OG',
                'translations' => '{"en":"Ogun State","ar":"Ogun ولاية","fr":"Ogun État","es":"Ogun Estado","de":"Ogun Staat"}',
                'latitude' => '6.99797470',
                'longitude' => '3.47373780',
            ),
            323 => 
            array (
                'id' => 324,
                'name' => 'Rukungiri District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '412',
                'translations' => '{"en":"Rukungiri District","ar":"Rukungiri مقاطعة","fr":"Rukungiri District","es":"Rukungiri Distrito","de":"Rukungiri Bezirk"}',
                'latitude' => '-0.75184900',
                'longitude' => '29.92779470',
            ),
            324 => 
            array (
                'id' => 325,
                'name' => 'Kyankwanzi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '123',
                'translations' => '{"en":"Kyankwanzi District","ar":"Kyankwanzi مقاطعة","fr":"Kyankwanzi District","es":"Kyankwanzi Distrito","de":"Kyankwanzi Bezirk"}',
                'latitude' => '1.09660370',
                'longitude' => '31.71954590',
            ),
            325 => 
            array (
                'id' => 326,
                'name' => 'Kabarole District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '405',
                'translations' => '{"en":"Kabarole District","ar":"Kabarole مقاطعة","fr":"Kabarole District","es":"Kabarole Distrito","de":"Kabarole Bezirk"}',
                'latitude' => '0.58507910',
                'longitude' => '30.25127280',
            ),
            326 => 
            array (
                'id' => 327,
                'name' => 'Mpigi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '106',
                'translations' => '{"en":"Mpigi District","ar":"Mpigi مقاطعة","fr":"Mpigi District","es":"Mpigi Distrito","de":"Mpigi Bezirk"}',
                'latitude' => '0.22735280',
                'longitude' => '32.32492360',
            ),
            327 => 
            array (
                'id' => 328,
                'name' => 'Apac District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '302',
                'translations' => '{"en":"Apac District","ar":"Apac مقاطعة","fr":"Apac District","es":"Apac Distrito","de":"Apac Bezirk"}',
                'latitude' => '1.87302630',
                'longitude' => '32.62774550',
            ),
            328 => 
            array (
                'id' => 329,
                'name' => 'Abim District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '314',
                'translations' => '{"en":"Abim District","ar":"Abim مقاطعة","fr":"Abim District","es":"Abim Distrito","de":"Abim Bezirk"}',
                'latitude' => '2.70669800',
                'longitude' => '33.65953370',
            ),
            329 => 
            array (
                'id' => 330,
                'name' => 'Yumbe District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '313',
                'translations' => '{"en":"Yumbe District","ar":"Yumbe مقاطعة","fr":"Yumbe District","es":"Yumbe Distrito","de":"Yumbe Bezirk"}',
                'latitude' => '3.46980230',
                'longitude' => '31.24832910',
            ),
            330 => 
            array (
                'id' => 331,
                'name' => 'Rukiga District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '431',
                'translations' => '{"en":"Rukiga District","ar":"Rukiga مقاطعة","fr":"Rukiga District","es":"Rukiga Distrito","de":"Rukiga Bezirk"}',
                'latitude' => '-1.13263370',
                'longitude' => '30.04341200',
            ),
            331 => 
            array (
                'id' => 332,
                'name' => 'Northern Region',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => 'N',
                'translations' => '{"en":"Northern Region","ar":"Northern منطقة","fr":"Northern Région","es":"Northern Región","de":"Northern Region"}',
                'latitude' => '9.54392690',
                'longitude' => '-0.90566230',
            ),
            332 => 
            array (
                'id' => 333,
                'name' => 'Serere District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '232',
                'translations' => '{"en":"Serere District","ar":"Serere مقاطعة","fr":"Serere District","es":"Serere Distrito","de":"Serere Bezirk"}',
                'latitude' => '1.49940330',
                'longitude' => '33.54900780',
            ),
            333 => 
            array (
                'id' => 334,
                'name' => 'Kamuli District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '205',
                'translations' => '{"en":"Kamuli District","ar":"Kamuli مقاطعة","fr":"Kamuli District","es":"Kamuli Distrito","de":"Kamuli Bezirk"}',
                'latitude' => '0.91871070',
                'longitude' => '33.12390490',
            ),
            334 => 
            array (
                'id' => 335,
                'name' => 'Amuru District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '316',
                'translations' => '{"en":"Amuru District","ar":"Amuru مقاطعة","fr":"Amuru District","es":"Amuru Distrito","de":"Amuru Bezirk"}',
                'latitude' => '2.96678780',
                'longitude' => '32.08374450',
            ),
            335 => 
            array (
                'id' => 336,
                'name' => 'Kaberamaido District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '213',
                'translations' => '{"en":"Kaberamaido District","ar":"Kaberamaido مقاطعة","fr":"Kaberamaido District","es":"Kaberamaido Distrito","de":"Kaberamaido Bezirk"}',
                'latitude' => '1.69633220',
                'longitude' => '33.21385100',
            ),
            336 => 
            array (
                'id' => 337,
                'name' => 'Namutumba District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '224',
                'translations' => '{"en":"Namutumba District","ar":"Namutumba مقاطعة","fr":"Namutumba District","es":"Namutumba Distrito","de":"Namutumba Bezirk"}',
                'latitude' => '0.84926100',
                'longitude' => '33.66233010',
            ),
            337 => 
            array (
                'id' => 338,
                'name' => 'Kibuku District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '227',
                'translations' => '{"en":"Kibuku District","ar":"Kibuku مقاطعة","fr":"Kibuku District","es":"Kibuku Distrito","de":"Kibuku Bezirk"}',
                'latitude' => '1.04528740',
                'longitude' => '33.79925360',
            ),
            338 => 
            array (
                'id' => 339,
                'name' => 'Ibanda District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '417',
                'translations' => '{"en":"Ibanda District","ar":"Ibanda مقاطعة","fr":"Ibanda District","es":"Ibanda Distrito","de":"Ibanda Bezirk"}',
                'latitude' => '-0.09648900',
                'longitude' => '30.57395790',
            ),
            339 => 
            array (
                'id' => 340,
                'name' => 'Iganga District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '203',
                'translations' => '{"en":"Iganga District","ar":"Iganga مقاطعة","fr":"Iganga District","es":"Iganga Distrito","de":"Iganga Bezirk"}',
                'latitude' => '0.66001370',
                'longitude' => '33.48319060',
            ),
            340 => 
            array (
                'id' => 341,
                'name' => 'Dokolo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '317',
                'translations' => '{"en":"Dokolo District","ar":"Dokolo مقاطعة","fr":"Dokolo District","es":"Dokolo Distrito","de":"Dokolo Bezirk"}',
                'latitude' => '1.96364210',
                'longitude' => '33.03387670',
            ),
            341 => 
            array (
                'id' => 342,
                'name' => 'Lira District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '307',
                'translations' => '{"en":"Lira District","ar":"Lira مقاطعة","fr":"Lira District","es":"Lira Distrito","de":"Lira Bezirk"}',
                'latitude' => '2.23161690',
                'longitude' => '32.94376670',
            ),
            342 => 
            array (
                'id' => 343,
                'name' => 'Bukedea District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '219',
                'translations' => '{"en":"Bukedea District","ar":"Bukedea مقاطعة","fr":"Bukedea District","es":"Bukedea Distrito","de":"Bukedea Bezirk"}',
                'latitude' => '1.35568980',
                'longitude' => '34.10867930',
            ),
            343 => 
            array (
                'id' => 344,
                'name' => 'Alebtong District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '323',
                'translations' => '{"en":"Alebtong District","ar":"Alebtong مقاطعة","fr":"Alebtong District","es":"Alebtong Distrito","de":"Alebtong Bezirk"}',
                'latitude' => '2.25457730',
                'longitude' => '33.34861470',
            ),
            344 => 
            array (
                'id' => 345,
                'name' => 'Koboko District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '319',
                'translations' => '{"en":"Koboko District","ar":"Koboko مقاطعة","fr":"Koboko District","es":"Koboko Distrito","de":"Koboko Bezirk"}',
                'latitude' => '3.52370580',
                'longitude' => '31.03351000',
            ),
            345 => 
            array (
                'id' => 346,
                'name' => 'Kiryandongo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '421',
                'translations' => '{"en":"Kiryandongo District","ar":"Kiryandongo مقاطعة","fr":"Kiryandongo District","es":"Kiryandongo Distrito","de":"Kiryandongo Bezirk"}',
                'latitude' => '2.01799070',
                'longitude' => '32.08374450',
            ),
            346 => 
            array (
                'id' => 347,
                'name' => 'Kiboga District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '103',
                'translations' => '{"en":"Kiboga District","ar":"Kiboga مقاطعة","fr":"Kiboga District","es":"Kiboga Distrito","de":"Kiboga Bezirk"}',
                'latitude' => '0.96575900',
                'longitude' => '31.71954590',
            ),
            347 => 
            array (
                'id' => 348,
                'name' => 'Kitgum District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '305',
                'translations' => '{"en":"Kitgum District","ar":"Kitgum مقاطعة","fr":"Kitgum District","es":"Kitgum Distrito","de":"Kitgum Bezirk"}',
                'latitude' => '3.33968290',
                'longitude' => '33.16888830',
            ),
            348 => 
            array (
                'id' => 349,
                'name' => 'Bududa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '218',
                'translations' => '{"en":"Bududa District","ar":"Bududa مقاطعة","fr":"Bududa District","es":"Bududa Distrito","de":"Bududa Bezirk"}',
                'latitude' => '1.00296930',
                'longitude' => '34.33381230',
            ),
            349 => 
            array (
                'id' => 350,
                'name' => 'Mbale District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '209',
                'translations' => '{"en":"Mbale District","ar":"Mbale مقاطعة","fr":"Mbale District","es":"Mbale Distrito","de":"Mbale Bezirk"}',
                'latitude' => '1.03442740',
                'longitude' => '34.19768820',
            ),
            350 => 
            array (
                'id' => 351,
                'name' => 'Namayingo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '230',
                'translations' => '{"en":"Namayingo District","ar":"Namayingo مقاطعة","fr":"Namayingo District","es":"Namayingo Distrito","de":"Namayingo Bezirk"}',
                'latitude' => '-0.28035750',
                'longitude' => '33.75177230',
            ),
            351 => 
            array (
                'id' => 352,
                'name' => 'Amuria District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '216',
                'translations' => '{"en":"Amuria District","ar":"Amuria مقاطعة","fr":"Amuria District","es":"Amuria Distrito","de":"Amuria Bezirk"}',
                'latitude' => '2.03017000',
                'longitude' => '33.64275330',
            ),
            352 => 
            array (
                'id' => 353,
                'name' => 'Amudat District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '324',
                'translations' => '{"en":"Amudat District","ar":"Amudat مقاطعة","fr":"Amudat District","es":"Amudat Distrito","de":"Amudat Bezirk"}',
                'latitude' => '1.79162240',
                'longitude' => '34.90655100',
            ),
            353 => 
            array (
                'id' => 354,
                'name' => 'Masindi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '409',
                'translations' => '{"en":"Masindi District","ar":"Masindi مقاطعة","fr":"Masindi District","es":"Masindi Distrito","de":"Masindi Bezirk"}',
                'latitude' => '1.49203630',
                'longitude' => '31.71954590',
            ),
            354 => 
            array (
                'id' => 355,
                'name' => 'Kiruhura District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '419',
                'translations' => '{"en":"Kiruhura District","ar":"Kiruhura مقاطعة","fr":"Kiruhura District","es":"Kiruhura Distrito","de":"Kiruhura Bezirk"}',
                'latitude' => '-0.19279980',
                'longitude' => '30.80394740',
            ),
            355 => 
            array (
                'id' => 356,
                'name' => 'Masaka District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '105',
                'translations' => '{"en":"Masaka District","ar":"Masaka مقاطعة","fr":"Masaka District","es":"Masaka Distrito","de":"Masaka Bezirk"}',
                'latitude' => '-0.44636910',
                'longitude' => '31.90179540',
            ),
            356 => 
            array (
                'id' => 357,
                'name' => 'Pakwach District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '332',
                'translations' => '{"en":"Pakwach District","ar":"Pakwach مقاطعة","fr":"Pakwach District","es":"Pakwach Distrito","de":"Pakwach Bezirk"}',
                'latitude' => '2.46071410',
                'longitude' => '31.49417380',
            ),
            357 => 
            array (
                'id' => 358,
                'name' => 'Rubanda District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '429',
                'translations' => '{"en":"Rubanda District","ar":"Rubanda مقاطعة","fr":"Rubanda District","es":"Rubanda Distrito","de":"Rubanda Bezirk"}',
                'latitude' => '-1.18611900',
                'longitude' => '29.84535760',
            ),
            358 => 
            array (
                'id' => 359,
                'name' => 'Tororo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '212',
                'translations' => '{"en":"Tororo District","ar":"Tororo مقاطعة","fr":"Tororo District","es":"Tororo Distrito","de":"Tororo Bezirk"}',
                'latitude' => '0.68709940',
                'longitude' => '34.06414190',
            ),
            359 => 
            array (
                'id' => 360,
                'name' => 'Kamwenge District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '413',
                'translations' => '{"en":"Kamwenge District","ar":"Kamwenge مقاطعة","fr":"Kamwenge District","es":"Kamwenge Distrito","de":"Kamwenge Bezirk"}',
                'latitude' => '0.22579300',
                'longitude' => '30.48184460',
            ),
            360 => 
            array (
                'id' => 361,
                'name' => 'Adjumani District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '301',
                'translations' => '{"en":"Adjumani District","ar":"Adjumani مقاطعة","fr":"Adjumani District","es":"Adjumani Distrito","de":"Adjumani Bezirk"}',
                'latitude' => '3.25485270',
                'longitude' => '31.71954590',
            ),
            361 => 
            array (
                'id' => 362,
                'name' => 'Wakiso District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '113',
                'translations' => '{"en":"Wakiso District","ar":"Wakiso مقاطعة","fr":"Wakiso District","es":"Wakiso Distrito","de":"Wakiso Bezirk"}',
                'latitude' => '0.06301900',
                'longitude' => '32.44672380',
            ),
            362 => 
            array (
                'id' => 363,
                'name' => 'Moyo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '309',
                'translations' => '{"en":"Moyo District","ar":"Moyo مقاطعة","fr":"Moyo District","es":"Moyo Distrito","de":"Moyo Bezirk"}',
                'latitude' => '3.56964640',
                'longitude' => '31.67393710',
            ),
            363 => 
            array (
                'id' => 364,
                'name' => 'Mityana District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '115',
                'translations' => '{"en":"Mityana District","ar":"Mityana مقاطعة","fr":"Mityana District","es":"Mityana Distrito","de":"Mityana Bezirk"}',
                'latitude' => '0.44548450',
                'longitude' => '32.08374450',
            ),
            364 => 
            array (
                'id' => 365,
                'name' => 'Butaleja District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '221',
                'translations' => '{"en":"Butaleja District","ar":"Butaleja مقاطعة","fr":"Butaleja District","es":"Butaleja Distrito","de":"Butaleja Bezirk"}',
                'latitude' => '0.84749220',
                'longitude' => '33.84112880',
            ),
            365 => 
            array (
                'id' => 366,
                'name' => 'Gomba District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '121',
                'translations' => '{"en":"Gomba District","ar":"Gomba مقاطعة","fr":"Gomba District","es":"Gomba Distrito","de":"Gomba Bezirk"}',
                'latitude' => '0.22297910',
                'longitude' => '31.67393710',
            ),
            366 => 
            array (
                'id' => 367,
                'name' => 'Jinja District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '204',
                'translations' => '{"en":"Jinja District","ar":"Jinja مقاطعة","fr":"Jinja District","es":"Jinja Distrito","de":"Jinja Bezirk"}',
                'latitude' => '0.53437430',
                'longitude' => '33.30371430',
            ),
            367 => 
            array (
                'id' => 368,
                'name' => 'Kayunga District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '112',
                'translations' => '{"en":"Kayunga District","ar":"Kayunga مقاطعة","fr":"Kayunga District","es":"Kayunga Distrito","de":"Kayunga Bezirk"}',
                'latitude' => '0.98601820',
                'longitude' => '32.85357550',
            ),
            368 => 
            array (
                'id' => 369,
                'name' => 'Kween District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '228',
                'translations' => '{"en":"Kween District","ar":"Kween مقاطعة","fr":"Kween District","es":"Kween Distrito","de":"Kween Bezirk"}',
                'latitude' => '1.44387900',
                'longitude' => '34.59713200',
            ),
            369 => 
            array (
                'id' => 370,
                'name' => 'Western Region',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => 'W',
                'translations' => '{"en":"Western Region","ar":"Western منطقة","fr":"Western Région","es":"Western Región","de":"Western Region"}',
                'latitude' => '40.76672150',
                'longitude' => '-111.88772030',
            ),
            370 => 
            array (
                'id' => 371,
                'name' => 'Mubende District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '107',
                'translations' => '{"en":"Mubende District","ar":"Mubende مقاطعة","fr":"Mubende District","es":"Mubende Distrito","de":"Mubende Bezirk"}',
                'latitude' => '0.57727580',
                'longitude' => '31.53700030',
            ),
            371 => 
            array (
                'id' => 372,
                'name' => 'Eastern Region',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => 'E',
                'translations' => '{"en":"Eastern Region","ar":"Eastern منطقة","fr":"Eastern Région","es":"Eastern Región","de":"Eastern Region"}',
                'latitude' => '6.23740360',
                'longitude' => '-0.45023680',
            ),
            372 => 
            array (
                'id' => 373,
                'name' => 'Kanungu District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '414',
                'translations' => '{"en":"Kanungu District","ar":"Kanungu مقاطعة","fr":"Kanungu District","es":"Kanungu Distrito","de":"Kanungu Bezirk"}',
                'latitude' => '-0.81952530',
                'longitude' => '29.74260400',
            ),
            373 => 
            array (
                'id' => 374,
                'name' => 'Omoro District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '331',
                'translations' => '{"en":"Omoro District","ar":"Omoro مقاطعة","fr":"Omoro District","es":"Omoro Distrito","de":"Omoro Bezirk"}',
                'latitude' => '2.71522300',
                'longitude' => '32.49200880',
            ),
            374 => 
            array (
                'id' => 375,
                'name' => 'Bukomansimbi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '118',
                'translations' => '{"en":"Bukomansimbi District","ar":"Bukomansimbi مقاطعة","fr":"Bukomansimbi District","es":"Bukomansimbi Distrito","de":"Bukomansimbi Bezirk"}',
                'latitude' => '-0.14327520',
                'longitude' => '31.60548930',
            ),
            375 => 
            array (
                'id' => 376,
                'name' => 'Lyantonde District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '114',
                'translations' => '{"en":"Lyantonde District","ar":"Lyantonde مقاطعة","fr":"Lyantonde District","es":"Lyantonde Distrito","de":"Lyantonde Bezirk"}',
                'latitude' => '-0.22406960',
                'longitude' => '31.21684660',
            ),
            376 => 
            array (
                'id' => 377,
                'name' => 'Buikwe District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '117',
                'translations' => '{"en":"Buikwe District","ar":"Buikwe مقاطعة","fr":"Buikwe District","es":"Buikwe Distrito","de":"Buikwe Bezirk"}',
                'latitude' => '0.31440460',
                'longitude' => '32.98883190',
            ),
            377 => 
            array (
                'id' => 378,
                'name' => 'Nwoya District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '328',
                'translations' => '{"en":"Nwoya District","ar":"Nwoya مقاطعة","fr":"Nwoya District","es":"Nwoya Distrito","de":"Nwoya Bezirk"}',
                'latitude' => '2.56244400',
                'longitude' => '31.90179540',
            ),
            378 => 
            array (
                'id' => 379,
                'name' => 'Zombo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '330',
                'translations' => '{"en":"Zombo District","ar":"Zombo مقاطعة","fr":"Zombo District","es":"Zombo Distrito","de":"Zombo Bezirk"}',
                'latitude' => '2.55442930',
                'longitude' => '30.94173680',
            ),
            379 => 
            array (
                'id' => 380,
                'name' => 'Buyende District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '226',
                'translations' => '{"en":"Buyende District","ar":"Buyende مقاطعة","fr":"Buyende District","es":"Buyende Distrito","de":"Buyende Bezirk"}',
                'latitude' => '1.24136820',
                'longitude' => '33.12390490',
            ),
            380 => 
            array (
                'id' => 381,
                'name' => 'Bunyangabu District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '430',
                'translations' => '{"en":"Bunyangabu District","ar":"Bunyangabu مقاطعة","fr":"Bunyangabu District","es":"Bunyangabu Distrito","de":"Bunyangabu Bezirk"}',
                'latitude' => '0.48709180',
                'longitude' => '30.20510960',
            ),
            381 => 
            array (
                'id' => 382,
                'name' => 'Kampala District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '102',
                'translations' => '{"en":"Kampala District","ar":"Kampala مقاطعة","fr":"Kampala District","es":"Kampala Distrito","de":"Kampala Bezirk"}',
                'latitude' => '0.34759640',
                'longitude' => '32.58251970',
            ),
            382 => 
            array (
                'id' => 383,
                'name' => 'Isingiro District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '418',
                'translations' => '{"en":"Isingiro District","ar":"Isingiro مقاطعة","fr":"Isingiro District","es":"Isingiro Distrito","de":"Isingiro Bezirk"}',
                'latitude' => '-0.84354300',
                'longitude' => '30.80394740',
            ),
            383 => 
            array (
                'id' => 384,
                'name' => 'Butambala District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '119',
                'translations' => '{"en":"Butambala District","ar":"Butambala مقاطعة","fr":"Butambala District","es":"Butambala Distrito","de":"Butambala Bezirk"}',
                'latitude' => '0.17425000',
                'longitude' => '32.10646680',
            ),
            384 => 
            array (
                'id' => 385,
                'name' => 'Bukwo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '220',
                'translations' => '{"en":"Bukwo District","ar":"Bukwo مقاطعة","fr":"Bukwo District","es":"Bukwo Distrito","de":"Bukwo Bezirk"}',
                'latitude' => '1.28186510',
                'longitude' => '34.72987650',
            ),
            385 => 
            array (
                'id' => 386,
                'name' => 'Bushenyi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '402',
                'translations' => '{"en":"Bushenyi District","ar":"Bushenyi مقاطعة","fr":"Bushenyi District","es":"Bushenyi Distrito","de":"Bushenyi Bezirk"}',
                'latitude' => '-0.48709180',
                'longitude' => '30.20510960',
            ),
            386 => 
            array (
                'id' => 387,
                'name' => 'Bugiri District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '201',
                'translations' => '{"en":"Bugiri District","ar":"Bugiri مقاطعة","fr":"Bugiri District","es":"Bugiri Distrito","de":"Bugiri Bezirk"}',
                'latitude' => '0.53161270',
                'longitude' => '33.75177230',
            ),
            387 => 
            array (
                'id' => 388,
                'name' => 'Butebo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '233',
                'translations' => '{"en":"Butebo District","ar":"Butebo مقاطعة","fr":"Butebo District","es":"Butebo Distrito","de":"Butebo Bezirk"}',
                'latitude' => '1.21411240',
                'longitude' => '33.90808960',
            ),
            388 => 
            array (
                'id' => 389,
                'name' => 'Buliisa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '416',
                'translations' => '{"en":"Buliisa District","ar":"Buliisa مقاطعة","fr":"Buliisa District","es":"Buliisa Distrito","de":"Buliisa Bezirk"}',
                'latitude' => '2.02996070',
                'longitude' => '31.53700030',
            ),
            389 => 
            array (
                'id' => 390,
                'name' => 'Otuke District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '329',
                'translations' => '{"en":"Otuke District","ar":"Otuke مقاطعة","fr":"Otuke District","es":"Otuke Distrito","de":"Otuke Bezirk"}',
                'latitude' => '2.52140590',
                'longitude' => '33.34861470',
            ),
            390 => 
            array (
                'id' => 391,
                'name' => 'Buhweju District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '420',
                'translations' => '{"en":"Buhweju District","ar":"Buhweju مقاطعة","fr":"Buhweju District","es":"Buhweju Distrito","de":"Buhweju Bezirk"}',
                'latitude' => '-0.29113590',
                'longitude' => '30.29741990',
            ),
            391 => 
            array (
                'id' => 392,
                'name' => 'Agago District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '322',
                'translations' => '{"en":"Agago District","ar":"Agago مقاطعة","fr":"Agago District","es":"Agago Distrito","de":"Agago Bezirk"}',
                'latitude' => '2.92508200',
                'longitude' => '33.34861470',
            ),
            392 => 
            array (
                'id' => 393,
                'name' => 'Nakapiripirit District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '311',
                'translations' => '{"en":"Nakapiripirit District","ar":"Nakapiripirit مقاطعة","fr":"Nakapiripirit District","es":"Nakapiripirit Distrito","de":"Nakapiripirit Bezirk"}',
                'latitude' => '1.96061730',
                'longitude' => '34.59713200',
            ),
            393 => 
            array (
                'id' => 394,
                'name' => 'Kalungu District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '122',
                'translations' => '{"en":"Kalungu District","ar":"Kalungu مقاطعة","fr":"Kalungu District","es":"Kalungu Distrito","de":"Kalungu Bezirk"}',
                'latitude' => '-0.09528310',
                'longitude' => '31.76513620',
            ),
            394 => 
            array (
                'id' => 395,
                'name' => 'Moroto District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '308',
                'translations' => '{"en":"Moroto District","ar":"Moroto مقاطعة","fr":"Moroto District","es":"Moroto Distrito","de":"Moroto Bezirk"}',
                'latitude' => '2.61685450',
                'longitude' => '34.59713200',
            ),
            395 => 
            array (
                'id' => 396,
                'name' => 'Central Region',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => 'C',
                'translations' => '{"en":"Central Region","ar":"Central منطقة","fr":"Central Région","es":"Central Región","de":"Central Region"}',
                'latitude' => '44.29687500',
                'longitude' => '-94.74017330',
            ),
            396 => 
            array (
                'id' => 397,
                'name' => 'Oyam District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '321',
                'translations' => '{"en":"Oyam District","ar":"Oyam مقاطعة","fr":"Oyam District","es":"Oyam Distrito","de":"Oyam Bezirk"}',
                'latitude' => '2.27762810',
                'longitude' => '32.44672380',
            ),
            397 => 
            array (
                'id' => 398,
                'name' => 'Kaliro District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '222',
                'translations' => '{"en":"Kaliro District","ar":"Kaliro مقاطعة","fr":"Kaliro District","es":"Kaliro Distrito","de":"Kaliro Bezirk"}',
                'latitude' => '1.04311070',
                'longitude' => '33.48319060',
            ),
            398 => 
            array (
                'id' => 399,
                'name' => 'Kakumiro District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '428',
                'translations' => '{"en":"Kakumiro District","ar":"Kakumiro مقاطعة","fr":"Kakumiro District","es":"Kakumiro Distrito","de":"Kakumiro Bezirk"}',
                'latitude' => '0.78080350',
                'longitude' => '31.32413890',
            ),
            399 => 
            array (
                'id' => 400,
                'name' => 'Namisindwa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '234',
                'translations' => '{"en":"Namisindwa District","ar":"Namisindwa مقاطعة","fr":"Namisindwa District","es":"Namisindwa Distrito","de":"Namisindwa Bezirk"}',
                'latitude' => '0.90710100',
                'longitude' => '34.35740370',
            ),
            400 => 
            array (
                'id' => 401,
                'name' => 'Kole District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '325',
                'translations' => '{"en":"Kole District","ar":"Kole مقاطعة","fr":"Kole District","es":"Kole Distrito","de":"Kole Bezirk"}',
                'latitude' => '2.37010970',
                'longitude' => '32.76330360',
            ),
            401 => 
            array (
                'id' => 402,
                'name' => 'Kyenjojo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '415',
                'translations' => '{"en":"Kyenjojo District","ar":"Kyenjojo مقاطعة","fr":"Kyenjojo District","es":"Kyenjojo Distrito","de":"Kyenjojo Bezirk"}',
                'latitude' => '0.60929230',
                'longitude' => '30.64012310',
            ),
            402 => 
            array (
                'id' => 403,
                'name' => 'Kagadi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '427',
                'translations' => '{"en":"Kagadi District","ar":"Kagadi مقاطعة","fr":"Kagadi District","es":"Kagadi Distrito","de":"Kagadi Bezirk"}',
                'latitude' => '0.94007610',
                'longitude' => '30.81256380',
            ),
            403 => 
            array (
                'id' => 404,
                'name' => 'Ntungamo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '411',
                'translations' => '{"en":"Ntungamo District","ar":"Ntungamo مقاطعة","fr":"Ntungamo District","es":"Ntungamo Distrito","de":"Ntungamo Bezirk"}',
                'latitude' => '-0.98073410',
                'longitude' => '30.25127280',
            ),
            404 => 
            array (
                'id' => 405,
                'name' => 'Kalangala District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '101',
                'translations' => '{"en":"Kalangala District","ar":"Kalangala مقاطعة","fr":"Kalangala District","es":"Kalangala Distrito","de":"Kalangala Bezirk"}',
                'latitude' => '-0.63505780',
                'longitude' => '32.53727410',
            ),
            405 => 
            array (
                'id' => 406,
                'name' => 'Nakasongola District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '109',
                'translations' => '{"en":"Nakasongola District","ar":"Nakasongola مقاطعة","fr":"Nakasongola District","es":"Nakasongola Distrito","de":"Nakasongola Bezirk"}',
                'latitude' => '1.34897210',
                'longitude' => '32.44672380',
            ),
            406 => 
            array (
                'id' => 407,
                'name' => 'Sheema District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '426',
                'translations' => '{"en":"Sheema District","ar":"Sheema مقاطعة","fr":"Sheema District","es":"Sheema Distrito","de":"Sheema Bezirk"}',
                'latitude' => '-0.55152980',
                'longitude' => '30.38966510',
            ),
            407 => 
            array (
                'id' => 408,
                'name' => 'Pader District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '312',
                'translations' => '{"en":"Pader District","ar":"Pader مقاطعة","fr":"Pader District","es":"Pader Distrito","de":"Pader Bezirk"}',
                'latitude' => '2.94306820',
                'longitude' => '32.80844960',
            ),
            408 => 
            array (
                'id' => 409,
                'name' => 'Kisoro District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '408',
                'translations' => '{"en":"Kisoro District","ar":"Kisoro مقاطعة","fr":"Kisoro District","es":"Kisoro Distrito","de":"Kisoro Bezirk"}',
                'latitude' => '-1.22094300',
                'longitude' => '29.64991620',
            ),
            409 => 
            array (
                'id' => 410,
                'name' => 'Mukono District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '108',
                'translations' => '{"en":"Mukono District","ar":"Mukono مقاطعة","fr":"Mukono District","es":"Mukono Distrito","de":"Mukono Bezirk"}',
                'latitude' => '0.28354760',
                'longitude' => '32.76330360',
            ),
            410 => 
            array (
                'id' => 411,
                'name' => 'Lamwo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '326',
                'translations' => '{"en":"Lamwo District","ar":"Lamwo مقاطعة","fr":"Lamwo District","es":"Lamwo Distrito","de":"Lamwo Bezirk"}',
                'latitude' => '3.57075680',
                'longitude' => '32.53727410',
            ),
            411 => 
            array (
                'id' => 412,
                'name' => 'Pallisa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '210',
                'translations' => '{"en":"Pallisa District","ar":"Pallisa مقاطعة","fr":"Pallisa District","es":"Pallisa Distrito","de":"Pallisa Bezirk"}',
                'latitude' => '1.23242060',
                'longitude' => '33.75177230',
            ),
            412 => 
            array (
                'id' => 413,
                'name' => 'Gulu District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '304',
                'translations' => '{"en":"Gulu District","ar":"Gulu مقاطعة","fr":"Gulu District","es":"Gulu Distrito","de":"Gulu Bezirk"}',
                'latitude' => '2.81857760',
                'longitude' => '32.44672380',
            ),
            413 => 
            array (
                'id' => 414,
                'name' => 'Buvuma District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '120',
                'translations' => '{"en":"Buvuma District","ar":"Buvuma مقاطعة","fr":"Buvuma District","es":"Buvuma Distrito","de":"Buvuma Bezirk"}',
                'latitude' => '-0.37649120',
                'longitude' => '33.25879300',
            ),
            414 => 
            array (
                'id' => 415,
                'name' => 'Mbarara District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '410',
                'translations' => '{"en":"Mbarara District","ar":"Mbarara مقاطعة","fr":"Mbarara District","es":"Mbarara Distrito","de":"Mbarara Bezirk"}',
                'latitude' => '-0.60715960',
                'longitude' => '30.65450220',
            ),
            415 => 
            array (
                'id' => 416,
                'name' => 'Amolatar District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '315',
                'translations' => '{"en":"Amolatar District","ar":"Amolatar مقاطعة","fr":"Amolatar District","es":"Amolatar Distrito","de":"Amolatar Bezirk"}',
                'latitude' => '1.60544020',
                'longitude' => '32.80844960',
            ),
            416 => 
            array (
                'id' => 417,
                'name' => 'Lwengo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '124',
                'translations' => '{"en":"Lwengo District","ar":"Lwengo مقاطعة","fr":"Lwengo District","es":"Lwengo Distrito","de":"Lwengo Bezirk"}',
                'latitude' => '-0.41652880',
                'longitude' => '31.39989950',
            ),
            417 => 
            array (
                'id' => 418,
                'name' => 'Mayuge District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '214',
                'translations' => '{"en":"Mayuge District","ar":"Mayuge مقاطعة","fr":"Mayuge District","es":"Mayuge Distrito","de":"Mayuge Bezirk"}',
                'latitude' => '-0.21829820',
                'longitude' => '33.57280270',
            ),
            418 => 
            array (
                'id' => 419,
                'name' => 'Bundibugyo District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '401',
                'translations' => '{"en":"Bundibugyo District","ar":"Bundibugyo مقاطعة","fr":"Bundibugyo District","es":"Bundibugyo Distrito","de":"Bundibugyo Bezirk"}',
                'latitude' => '0.68517630',
                'longitude' => '30.02029640',
            ),
            419 => 
            array (
                'id' => 420,
                'name' => 'Katakwi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '207',
                'translations' => '{"en":"Katakwi District","ar":"Katakwi مقاطعة","fr":"Katakwi District","es":"Katakwi Distrito","de":"Katakwi Bezirk"}',
                'latitude' => '1.97310300',
                'longitude' => '34.06414190',
            ),
            420 => 
            array (
                'id' => 421,
                'name' => 'Maracha District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '320',
                'translations' => '{"en":"Maracha District","ar":"Maracha مقاطعة","fr":"Maracha District","es":"Maracha Distrito","de":"Maracha Bezirk"}',
                'latitude' => '3.28731270',
                'longitude' => '30.94030230',
            ),
            421 => 
            array (
                'id' => 422,
                'name' => 'Ntoroko District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '424',
                'translations' => '{"en":"Ntoroko District","ar":"Ntoroko مقاطعة","fr":"Ntoroko District","es":"Ntoroko Distrito","de":"Ntoroko Bezirk"}',
                'latitude' => '1.07881780',
                'longitude' => '30.38966510',
            ),
            422 => 
            array (
                'id' => 423,
                'name' => 'Nakaseke District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '116',
                'translations' => '{"en":"Nakaseke District","ar":"Nakaseke مقاطعة","fr":"Nakaseke District","es":"Nakaseke Distrito","de":"Nakaseke Bezirk"}',
                'latitude' => '1.22308480',
                'longitude' => '32.08374450',
            ),
            423 => 
            array (
                'id' => 424,
                'name' => 'Ngora District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '231',
                'translations' => '{"en":"Ngora District","ar":"Ngora مقاطعة","fr":"Ngora District","es":"Ngora Distrito","de":"Ngora Bezirk"}',
                'latitude' => '1.49081150',
                'longitude' => '33.75177230',
            ),
            424 => 
            array (
                'id' => 425,
                'name' => 'Kumi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '208',
                'translations' => '{"en":"Kumi District","ar":"Kumi مقاطعة","fr":"Kumi District","es":"Kumi Distrito","de":"Kumi Bezirk"}',
                'latitude' => '1.48769990',
                'longitude' => '33.93039910',
            ),
            425 => 
            array (
                'id' => 426,
                'name' => 'Kabale District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '404',
                'translations' => '{"en":"Kabale District","ar":"Kabale مقاطعة","fr":"Kabale District","es":"Kabale Distrito","de":"Kabale Bezirk"}',
                'latitude' => '-1.24930840',
                'longitude' => '30.06652360',
            ),
            426 => 
            array (
                'id' => 427,
                'name' => 'Sembabule District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '111',
                'translations' => '{"en":"Sembabule District","ar":"Sembabule مقاطعة","fr":"Sembabule District","es":"Sembabule Distrito","de":"Sembabule Bezirk"}',
                'latitude' => '0.06377150',
                'longitude' => '31.35416310',
            ),
            427 => 
            array (
                'id' => 428,
                'name' => 'Bulambuli District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '225',
                'translations' => '{"en":"Bulambuli District","ar":"Bulambuli مقاطعة","fr":"Bulambuli District","es":"Bulambuli Distrito","de":"Bulambuli Bezirk"}',
                'latitude' => '1.47988460',
                'longitude' => '34.37544140',
            ),
            428 => 
            array (
                'id' => 429,
                'name' => 'Sironko District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '215',
                'translations' => '{"en":"Sironko District","ar":"Sironko مقاطعة","fr":"Sironko District","es":"Sironko Distrito","de":"Sironko Bezirk"}',
                'latitude' => '1.23022740',
                'longitude' => '34.24910640',
            ),
            429 => 
            array (
                'id' => 430,
                'name' => 'Napak District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '327',
                'translations' => '{"en":"Napak District","ar":"Napak مقاطعة","fr":"Napak District","es":"Napak Distrito","de":"Napak Bezirk"}',
                'latitude' => '2.36299450',
                'longitude' => '34.24215970',
            ),
            430 => 
            array (
                'id' => 431,
                'name' => 'Busia District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '202',
                'translations' => '{"en":"Busia District","ar":"Busia مقاطعة","fr":"Busia District","es":"Busia Distrito","de":"Busia Bezirk"}',
                'latitude' => '0.40447310',
                'longitude' => '34.01958270',
            ),
            431 => 
            array (
                'id' => 432,
                'name' => 'Kapchorwa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '206',
                'translations' => '{"en":"Kapchorwa District","ar":"Kapchorwa مقاطعة","fr":"Kapchorwa District","es":"Kapchorwa Distrito","de":"Kapchorwa Bezirk"}',
                'latitude' => '1.33502050',
                'longitude' => '34.39763560',
            ),
            432 => 
            array (
                'id' => 433,
                'name' => 'Luwero District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '104',
                'translations' => '{"en":"Luwero District","ar":"Luwero مقاطعة","fr":"Luwero District","es":"Luwero Distrito","de":"Luwero Bezirk"}',
                'latitude' => '0.82711180',
                'longitude' => '32.62774550',
            ),
            433 => 
            array (
                'id' => 434,
                'name' => 'Kaabong District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '318',
                'translations' => '{"en":"Kaabong District","ar":"Kaabong مقاطعة","fr":"Kaabong District","es":"Kaabong Distrito","de":"Kaabong Bezirk"}',
                'latitude' => '3.51262150',
                'longitude' => '33.97500180',
            ),
            434 => 
            array (
                'id' => 435,
                'name' => 'Mitooma District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '423',
                'translations' => '{"en":"Mitooma District","ar":"Mitooma مقاطعة","fr":"Mitooma District","es":"Mitooma Distrito","de":"Mitooma Bezirk"}',
                'latitude' => '-0.61932760',
                'longitude' => '30.02029640',
            ),
            435 => 
            array (
                'id' => 436,
                'name' => 'Kibaale District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '407',
                'translations' => '{"en":"Kibaale District","ar":"Kibaale مقاطعة","fr":"Kibaale District","es":"Kibaale Distrito","de":"Kibaale Bezirk"}',
                'latitude' => '0.90668020',
                'longitude' => '31.07937050',
            ),
            436 => 
            array (
                'id' => 437,
                'name' => 'Kyegegwa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '422',
                'translations' => '{"en":"Kyegegwa District","ar":"Kyegegwa مقاطعة","fr":"Kyegegwa District","es":"Kyegegwa Distrito","de":"Kyegegwa Bezirk"}',
                'latitude' => '0.48181930',
                'longitude' => '31.05500930',
            ),
            437 => 
            array (
                'id' => 438,
                'name' => 'Manafwa District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '223',
                'translations' => '{"en":"Manafwa District","ar":"Manafwa مقاطعة","fr":"Manafwa District","es":"Manafwa Distrito","de":"Manafwa Bezirk"}',
                'latitude' => '0.90635990',
                'longitude' => '34.28660910',
            ),
            438 => 
            array (
                'id' => 439,
                'name' => 'Rakai District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '110',
                'translations' => '{"en":"Rakai District","ar":"Rakai مقاطعة","fr":"Rakai District","es":"Rakai Distrito","de":"Rakai Bezirk"}',
                'latitude' => '-0.70691350',
                'longitude' => '31.53700030',
            ),
            439 => 
            array (
                'id' => 440,
                'name' => 'Kasese District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '406',
                'translations' => '{"en":"Kasese District","ar":"Kasese مقاطعة","fr":"Kasese District","es":"Kasese Distrito","de":"Kasese Bezirk"}',
                'latitude' => '0.06462850',
                'longitude' => '30.06652360',
            ),
            440 => 
            array (
                'id' => 441,
                'name' => 'Budaka District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '217',
                'translations' => '{"en":"Budaka District","ar":"Budaka مقاطعة","fr":"Budaka District","es":"Budaka Distrito","de":"Budaka Bezirk"}',
                'latitude' => '1.10162770',
                'longitude' => '33.93039910',
            ),
            441 => 
            array (
                'id' => 442,
                'name' => 'Rubirizi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '425',
                'translations' => '{"en":"Rubirizi District","ar":"Rubirizi مقاطعة","fr":"Rubirizi District","es":"Rubirizi Distrito","de":"Rubirizi Bezirk"}',
                'latitude' => '-0.26424100',
                'longitude' => '30.10840330',
            ),
            442 => 
            array (
                'id' => 443,
                'name' => 'Kotido District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '306',
                'translations' => '{"en":"Kotido District","ar":"Kotido مقاطعة","fr":"Kotido District","es":"Kotido Distrito","de":"Kotido Bezirk"}',
                'latitude' => '3.04156790',
                'longitude' => '33.88577470',
            ),
            443 => 
            array (
                'id' => 444,
                'name' => 'Soroti District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '211',
                'translations' => '{"en":"Soroti District","ar":"Soroti مقاطعة","fr":"Soroti District","es":"Soroti Distrito","de":"Soroti Bezirk"}',
                'latitude' => '1.72291170',
                'longitude' => '33.52800720',
            ),
            444 => 
            array (
                'id' => 445,
                'name' => 'Luuka District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '229',
                'translations' => '{"en":"Luuka District","ar":"Luuka مقاطعة","fr":"Luuka District","es":"Luuka Distrito","de":"Luuka Bezirk"}',
                'latitude' => '0.72505990',
                'longitude' => '33.30371430',
            ),
            445 => 
            array (
                'id' => 446,
                'name' => 'Nebbi District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '310',
                'translations' => '{"en":"Nebbi District","ar":"Nebbi مقاطعة","fr":"Nebbi District","es":"Nebbi Distrito","de":"Nebbi Bezirk"}',
                'latitude' => '2.44093920',
                'longitude' => '31.35416310',
            ),
            446 => 
            array (
                'id' => 447,
                'name' => 'Arua District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '303',
                'translations' => '{"en":"Arua District","ar":"Arua مقاطعة","fr":"Arua District","es":"Arua Distrito","de":"Arua Bezirk"}',
                'latitude' => '2.99598460',
                'longitude' => '31.17103890',
            ),
            447 => 
            array (
                'id' => 448,
                'name' => 'Kyotera District',
                'country_id' => 229,
                'country_code' => 'UG',
                'iso2' => '125',
                'translations' => '{"en":"Kyotera District","ar":"Kyotera مقاطعة","fr":"Kyotera District","es":"Kyotera Distrito","de":"Kyotera Bezirk"}',
                'latitude' => '-0.63589880',
                'longitude' => '31.54556370',
            ),
            448 => 
            array (
                'id' => 449,
                'name' => 'Schellenberg',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '08',
                'translations' => '{"en":"Schellenberg","ar":"Schellenberg","fr":"Schellenberg","es":"Schellenberg","de":"Schellenberg"}',
                'latitude' => '47.23096600',
                'longitude' => '9.54678430',
            ),
            449 => 
            array (
                'id' => 450,
                'name' => 'Schaan',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '07',
                'translations' => '{"en":"Schaan","ar":"Schaan","fr":"Schaan","es":"Schaan","de":"Schaan"}',
                'latitude' => '47.12043400',
                'longitude' => '9.59416020',
            ),
            450 => 
            array (
                'id' => 451,
                'name' => 'Eschen',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '02',
                'translations' => '{"en":"Eschen","ar":"Eschen","fr":"Eschen","es":"Eschen","de":"Eschen"}',
                'latitude' => '40.76695740',
                'longitude' => '-73.95228210',
            ),
            451 => 
            array (
                'id' => 452,
                'name' => 'Vaduz',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '11',
                'translations' => '{"en":"Vaduz","ar":"Vaduz","fr":"Vaduz","es":"Vaduz","de":"Vaduz"}',
                'latitude' => '47.14103030',
                'longitude' => '9.52092770',
            ),
            452 => 
            array (
                'id' => 453,
                'name' => 'Ruggell',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '06',
                'translations' => '{"en":"Ruggell","ar":"Ruggell","fr":"Ruggell","es":"Ruggell","de":"Ruggell"}',
                'latitude' => '47.25292220',
                'longitude' => '9.54021270',
            ),
            453 => 
            array (
                'id' => 454,
                'name' => 'Planken',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '05',
                'translations' => '{"en":"Planken","ar":"Planken","fr":"Planken","es":"Planken","de":"Planken"}',
                'latitude' => '40.66505760',
                'longitude' => '-73.50479800',
            ),
            454 => 
            array (
                'id' => 455,
                'name' => 'Mauren',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '04',
                'translations' => '{"en":"Mauren","ar":"Mauren","fr":"Mauren","es":"Mauren","de":"Mauren"}',
                'latitude' => '47.21892850',
                'longitude' => '9.54173500',
            ),
            455 => 
            array (
                'id' => 456,
                'name' => 'Triesenberg',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '10',
                'translations' => '{"en":"Triesenberg","ar":"Triesenberg","fr":"Triesenberg","es":"Triesenberg","de":"Triesenberg"}',
                'latitude' => '47.12245110',
                'longitude' => '9.57019850',
            ),
            456 => 
            array (
                'id' => 457,
                'name' => 'Gamprin',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '03',
                'translations' => '{"en":"Gamprin","ar":"Gamprin","fr":"Gamprin","es":"Gamprin","de":"Gamprin"}',
                'latitude' => '47.21324900',
                'longitude' => '9.50251950',
            ),
            457 => 
            array (
                'id' => 458,
                'name' => 'Balzers',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '01',
                'translations' => '{"en":"Balzers","ar":"Balzers","fr":"Balzers","es":"Balzers","de":"Balzers"}',
                'latitude' => '42.05283570',
                'longitude' => '-88.03668480',
            ),
            458 => 
            array (
                'id' => 459,
                'name' => 'Triesen',
                'country_id' => 125,
                'country_code' => 'LI',
                'iso2' => '09',
                'translations' => '{"en":"Triesen","ar":"Triesen","fr":"Triesen","es":"Triesen","de":"Triesen"}',
                'latitude' => '47.10979880',
                'longitude' => '9.52482960',
            ),
            459 => 
            array (
                'id' => 460,
                'name' => 'Brčko District',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => 'BRC',
                'translations' => '{"en":"Brčko District","ar":"Brčko مقاطعة","fr":"Brčko District","es":"Brčko Distrito","de":"Brčko Bezirk"}',
                'latitude' => '44.84059440',
                'longitude' => '18.74215300',
            ),
            460 => 
            array (
                'id' => 461,
                'name' => 'Tuzla Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '03',
                'translations' => '{"en":"Tuzla Canton","ar":"Tuzla Canton","fr":"Tuzla Canton","es":"Tuzla Canton","de":"Tuzla Canton"}',
                'latitude' => '44.53434630',
                'longitude' => '18.69727970',
            ),
            461 => 
            array (
                'id' => 462,
                'name' => 'Central Bosnia Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '06',
                'translations' => '{"en":"Central Bosnia Canton","ar":"Central Bosnia Canton","fr":"Central Bosnia Canton","es":"Central Bosnia Canton","de":"Central Bosnia Canton"}',
                'latitude' => '44.13818560',
                'longitude' => '17.68667140',
            ),
            462 => 
            array (
                'id' => 463,
                'name' => 'Herzegovina-Neretva Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '07',
                'translations' => '{"en":"Herzegovina-Neretva Canton","ar":"Herzegovina-Neretva Canton","fr":"Herzegovina-Neretva Canton","es":"Herzegovina-Neretva Canton","de":"Herzegovina-Neretva Canton"}',
                'latitude' => '43.52651590',
                'longitude' => '17.76362100',
            ),
            463 => 
            array (
                'id' => 464,
                'name' => 'Posavina Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '02',
                'translations' => '{"en":"Posavina Canton","ar":"Posavina Canton","fr":"Posavina Canton","es":"Posavina Canton","de":"Posavina Canton"}',
                'latitude' => '45.07520940',
                'longitude' => '18.37763040',
            ),
            464 => 
            array (
                'id' => 465,
                'name' => 'Una-Sana Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '01',
                'translations' => '{"en":"Una-Sana Canton","ar":"Una-Sana Canton","fr":"Una-Sana Canton","es":"Una-Sana Canton","de":"Una-Sana Canton"}',
                'latitude' => '44.65031160',
                'longitude' => '16.31716290',
            ),
            465 => 
            array (
                'id' => 466,
                'name' => 'Sarajevo Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '09',
                'translations' => '{"en":"Sarajevo Canton","ar":"Sarajevo Canton","fr":"Sarajevo Canton","es":"Sarajevo Canton","de":"Sarajevo Canton"}',
                'latitude' => '43.85125640',
                'longitude' => '18.29534420',
            ),
            466 => 
            array (
                'id' => 467,
                'name' => 'Federation of Bosnia and Herzegovina',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => 'BIH',
                'translations' => '{"en":"Federation of Bosnia and Herzegovina","ar":"Federation of Bosnia and Herzegovina","fr":"Federation of Bosnia and Herzegovina","es":"Federation of Bosnia and Herzegovina","de":"Federation of Bosnia and Herzegovina"}',
                'latitude' => '43.88748970',
                'longitude' => '17.84279300',
            ),
            467 => 
            array (
                'id' => 468,
                'name' => 'Zenica-Doboj Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '04',
                'translations' => '{"en":"Zenica-Doboj Canton","ar":"Zenica-Doboj Canton","fr":"Zenica-Doboj Canton","es":"Zenica-Doboj Canton","de":"Zenica-Doboj Canton"}',
                'latitude' => '44.21271090',
                'longitude' => '18.16046250',
            ),
            468 => 
            array (
                'id' => 469,
                'name' => 'West Herzegovina Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '08',
                'translations' => '{"en":"West Herzegovina Canton","ar":"West Herzegovina Canton","fr":"West Herzegovina Canton","es":"West Herzegovina Canton","de":"West Herzegovina Canton"}',
                'latitude' => '43.43692440',
                'longitude' => '17.38488310',
            ),
            469 => 
            array (
                'id' => 470,
                'name' => 'Republika Srpska',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => 'SRP',
                'translations' => '{"en":"Republika Srpska","ar":"Republika Srpska","fr":"Republika Srpska","es":"Republika Srpska","de":"Republika Srpska"}',
                'latitude' => '44.72801860',
                'longitude' => '17.31481360',
            ),
            470 => 
            array (
                'id' => 471,
                'name' => 'Canton 10',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '10',
                'translations' => '{"en":"Canton 10","ar":"Canton 10","fr":"Canton 10","es":"Canton 10","de":"Canton 10"}',
                'latitude' => '43.95341550',
                'longitude' => '16.94251870',
            ),
            471 => 
            array (
                'id' => 472,
                'name' => 'Bosnian Podrinje Canton',
                'country_id' => 28,
                'country_code' => 'BA',
                'iso2' => '05',
                'translations' => '{"en":"Bosnian Podrinje Canton","ar":"Bosnian Podrinje Canton","fr":"Bosnian Podrinje Canton","es":"Bosnian Podrinje Canton","de":"Bosnian Podrinje Canton"}',
                'latitude' => '43.68749000',
                'longitude' => '18.82443940',
            ),
            472 => 
            array (
                'id' => 473,
                'name' => 'Dakar',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'DK',
                'translations' => '{"en":"Dakar","ar":"Dakar","fr":"Dakar","es":"Dakar","de":"Dakar"}',
                'latitude' => '14.71667700',
                'longitude' => '-17.46768610',
            ),
            473 => 
            array (
                'id' => 474,
                'name' => 'Kolda',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'KD',
                'translations' => '{"en":"Kolda","ar":"Kolda","fr":"Kolda","es":"Kolda","de":"Kolda"}',
                'latitude' => '12.91074950',
                'longitude' => '-14.95056710',
            ),
            474 => 
            array (
                'id' => 475,
                'name' => 'Kaffrine',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'KA',
                'translations' => '{"en":"Kaffrine","ar":"Kaffrine","fr":"Kaffrine","es":"Kaffrine","de":"Kaffrine"}',
                'latitude' => '14.10520200',
                'longitude' => '-15.54157550',
            ),
            475 => 
            array (
                'id' => 476,
                'name' => 'Matam',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'MT',
                'translations' => '{"en":"Matam","ar":"Matam","fr":"Matam","es":"Matam","de":"Matam"}',
                'latitude' => '15.66002250',
                'longitude' => '-13.25769060',
            ),
            476 => 
            array (
                'id' => 477,
                'name' => 'Saint-Louis',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'SL',
                'translations' => '{"en":"Saint-Louis","ar":"Saint-Louis","fr":"Saint-Louis","es":"Saint-Louis","de":"Saint-Louis"}',
                'latitude' => '38.62700250',
                'longitude' => '-90.19940420',
            ),
            477 => 
            array (
                'id' => 478,
                'name' => 'Ziguinchor',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'ZG',
                'translations' => '{"en":"Ziguinchor","ar":"Ziguinchor","fr":"Ziguinchor","es":"Ziguinchor","de":"Ziguinchor"}',
                'latitude' => '12.56414790',
                'longitude' => '-16.26398250',
            ),
            478 => 
            array (
                'id' => 479,
                'name' => 'Fatick',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'FK',
                'translations' => '{"en":"Fatick","ar":"Fatick","fr":"Fatick","es":"Fatick","de":"Fatick"}',
                'latitude' => '14.33901670',
                'longitude' => '-16.41114250',
            ),
            479 => 
            array (
                'id' => 480,
                'name' => 'Diourbel Region',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'DB',
                'translations' => '{"en":"Diourbel Region","ar":"Diourbel منطقة","fr":"Diourbel Région","es":"Diourbel Región","de":"Diourbel Region"}',
                'latitude' => '14.72830850',
                'longitude' => '-16.25221430',
            ),
            480 => 
            array (
                'id' => 481,
                'name' => 'Kédougou',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'KE',
                'translations' => '{"en":"Kédougou","ar":"Kédougou","fr":"Kédougou","es":"Kédougou","de":"Kédougou"}',
                'latitude' => '12.56046070',
                'longitude' => '-12.17470770',
            ),
            481 => 
            array (
                'id' => 482,
                'name' => 'Sédhiou',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'SE',
                'translations' => '{"en":"Sédhiou","ar":"Sédhiou","fr":"Sédhiou","es":"Sédhiou","de":"Sédhiou"}',
                'latitude' => '12.70460400',
                'longitude' => '-15.55623040',
            ),
            482 => 
            array (
                'id' => 483,
                'name' => 'Kaolack',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'KL',
                'translations' => '{"en":"Kaolack","ar":"Kaolack","fr":"Kaolack","es":"Kaolack","de":"Kaolack"}',
                'latitude' => '14.16520830',
                'longitude' => '-16.07577490',
            ),
            483 => 
            array (
                'id' => 484,
                'name' => 'Thiès Region',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'TH',
                'translations' => '{"en":"Thiès Region","ar":"Thiès منطقة","fr":"Thiès Région","es":"Thiès Región","de":"Thiès Region"}',
                'latitude' => '14.79100520',
                'longitude' => '-16.93586040',
            ),
            484 => 
            array (
                'id' => 485,
                'name' => 'Louga',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'LG',
                'translations' => '{"en":"Louga","ar":"Louga","fr":"Louga","es":"Louga","de":"Louga"}',
                'latitude' => '15.61417680',
                'longitude' => '-16.22868000',
            ),
            485 => 
            array (
                'id' => 486,
                'name' => 'Tambacounda Region',
                'country_id' => 195,
                'country_code' => 'SN',
                'iso2' => 'TC',
                'translations' => '{"en":"Tambacounda Region","ar":"Tambacounda منطقة","fr":"Tambacounda Région","es":"Tambacounda Región","de":"Tambacounda Region"}',
                'latitude' => '13.56190110',
                'longitude' => '-13.17403480',
            ),
            486 => 
            array (
                'id' => 487,
                'name' => 'Encamp',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '03',
                'translations' => '{"en":"Encamp","ar":"Encamp","fr":"Encamp","es":"Encamp","de":"Encamp"}',
                'latitude' => '42.53597640',
                'longitude' => '1.58367730',
            ),
            487 => 
            array (
                'id' => 488,
                'name' => 'Andorra la Vella',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '07',
                'translations' => '{"en":"Andorra la Vella","ar":"Andorra la Vella","fr":"Andorra la Vella","es":"Andorra la Vella","de":"Andorra la Vella"}',
                'latitude' => '42.50631740',
                'longitude' => '1.52183550',
            ),
            488 => 
            array (
                'id' => 489,
                'name' => 'Canillo',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '02',
                'translations' => '{"en":"Canillo","ar":"Canillo","fr":"Canillo","es":"Canillo","de":"Canillo"}',
                'latitude' => '42.59782490',
                'longitude' => '1.65663770',
            ),
            489 => 
            array (
                'id' => 490,
                'name' => 'Sant Julià de Lòria',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '06',
                'translations' => '{"en":"Sant Julià de Lòria","ar":"Sant Julià de Lòria","fr":"Sant Julià de Lòria","es":"Sant Julià de Lòria","de":"Sant Julià de Lòria"}',
                'latitude' => '42.45296310',
                'longitude' => '1.49182350',
            ),
            490 => 
            array (
                'id' => 491,
                'name' => 'Ordino',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '05',
                'translations' => '{"en":"Ordino","ar":"Ordino","fr":"Ordino","es":"Ordino","de":"Ordino"}',
                'latitude' => '42.59944330',
                'longitude' => '1.54023270',
            ),
            491 => 
            array (
                'id' => 492,
                'name' => 'Escaldes-Engordany',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '08',
                'translations' => '{"en":"Escaldes-Engordany","ar":"Escaldes-Engordany","fr":"Escaldes-Engordany","es":"Escaldes-Engordany","de":"Escaldes-Engordany"}',
                'latitude' => '42.49093790',
                'longitude' => '1.58869660',
            ),
            492 => 
            array (
                'id' => 493,
                'name' => 'La Massana',
                'country_id' => 6,
                'country_code' => 'AD',
                'iso2' => '04',
                'translations' => '{"en":"La Massana","ar":"La Massana","fr":"La Massana","es":"La Massana","de":"La Massana"}',
                'latitude' => '42.54562500',
                'longitude' => '1.51473920',
            ),
            493 => 
            array (
                'id' => 494,
                'name' => 'Mont Buxton',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '17',
                'translations' => '{"en":"Mont Buxton","ar":"Mont Buxton","fr":"Mont Buxton","es":"Mont Buxton","de":"Mont Buxton"}',
                'latitude' => '-4.61666670',
                'longitude' => '55.44577680',
            ),
            494 => 
            array (
                'id' => 495,
                'name' => 'La Digue',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '15',
                'translations' => '{"en":"La Digue","ar":"La Digue","fr":"La Digue","es":"La Digue","de":"La Digue"}',
                'latitude' => '49.76669220',
                'longitude' => '-97.15466290',
            ),
            495 => 
            array (
                'id' => 496,
                'name' => 'Saint Louis',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '22',
                'translations' => '{"en":"Saint Louis","ar":"Saint Louis","fr":"Saint Louis","es":"Saint Louis","de":"Saint Louis"}',
                'latitude' => '38.62700250',
                'longitude' => '-90.19940420',
            ),
            496 => 
            array (
                'id' => 497,
                'name' => 'Baie Lazare',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '06',
                'translations' => '{"en":"Baie Lazare","ar":"Baie Lazare","fr":"Baie Lazare","es":"Baie Lazare","de":"Baie Lazare"}',
                'latitude' => '-4.74825250',
                'longitude' => '55.48593630',
            ),
            497 => 
            array (
                'id' => 498,
                'name' => 'Mont Fleuri',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '18',
                'translations' => '{"en":"Mont Fleuri","ar":"Mont Fleuri","fr":"Mont Fleuri","es":"Mont Fleuri","de":"Mont Fleuri"}',
                'latitude' => '-4.63565430',
                'longitude' => '55.45546880',
            ),
            498 => 
            array (
                'id' => 499,
                'name' => 'Les Mamelles',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '24',
                'translations' => '{"en":"Les Mamelles","ar":"Les Mamelles","fr":"Les Mamelles","es":"Les Mamelles","de":"Les Mamelles"}',
                'latitude' => '38.82505050',
                'longitude' => '-90.48345170',
            ),
            499 => 
            array (
                'id' => 500,
                'name' => 'Grand\'Anse Mahé',
                'country_id' => 197,
                'country_code' => 'SC',
                'iso2' => '13',
                'translations' => '{"en":"Grand\'Anse Mahé","ar":"Grand\'Anse Mahé","fr":"Grand\'Anse Mahé","es":"Grand\'Anse Mahé","de":"Grand\'Anse Mahé"}',
                'latitude' => '-4.67739200',
                'longitude' => '55.46377700',
            ),
        ));
    }
}
