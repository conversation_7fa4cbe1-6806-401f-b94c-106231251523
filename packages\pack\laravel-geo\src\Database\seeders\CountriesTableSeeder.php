<?php

namespace Pack\LaravelGeo\Database\seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountriesTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        DB::table('countries')->delete();

        DB::table('countries')->insert(array(
            0 =>
            array(
                'name' => 'Afghanistan',
                'iso3' => 'AFG',
                'iso2' => 'AF',
                'dial_code' => '93',
                'currency' => 'AFN',
                'currency_symbol' => '؋',
                'translations' => '{"br":"Afeganistão","pt":"Afeganistão","nl":"Afghanistan","hr":"Afganistan","fa":"افغانستان","de":"Afghanistan","es":"Afganistán","fr":"Afghanistan","ja":"アフガニスタン","it":"Afghanistan","ar":"أفغانستان"}',
                'latitude' => '33.00000000',
                'longitude' => '65.00000000',
            ),
            1 =>
            array(
                'name' => 'Aland Islands',
                'iso3' => 'ALA',
                'iso2' => 'AX',
                'dial_code' => '+358-18',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Ilhas de Aland","pt":"Ilhas de Aland","nl":"Ålandeilanden","hr":"Ålandski otoci","fa":"جزایر الند","de":"Åland","es":"Alandia","fr":"Åland","ja":"オーランド諸島","it":"Isole Aland","ar":"جزر آلاند"}',
                'latitude' => '60.11666700',
                'longitude' => '19.90000000',
            ),
            2 =>
            array(
                'name' => 'Albania',
                'iso3' => 'ALB',
                'iso2' => 'AL',
                'dial_code' => '355',
                'currency' => 'ALL',
                'currency_symbol' => 'Lek',
                'translations' => '{"br":"Albânia","pt":"Albânia","nl":"Albanië","hr":"Albanija","fa":"آلبانی","de":"Albanien","es":"Albania","fr":"Albanie","ja":"アルバニア","it":"Albania","ar":"ألبانيا"}',
                'latitude' => '41.00000000',
                'longitude' => '20.00000000',
            ),
            3 =>
            array(
                'name' => 'Algeria',
                'iso3' => 'DZA',
                'iso2' => 'DZ',
                'dial_code' => '213',
                'currency' => 'DZD',
                'currency_symbol' => 'دج',
                'translations' => '{"br":"Argélia","pt":"Argélia","nl":"Algerije","hr":"Alžir","fa":"الجزایر","de":"Algerien","es":"Argelia","fr":"Algérie","ja":"アルジェリア","it":"Algeria","ar":"الجزائر"}',
                'latitude' => '28.00000000',
                'longitude' => '3.00000000',
            ),
            4 =>
            array(
                'name' => 'American Samoa',
                'iso3' => 'ASM',
                'iso2' => 'AS',
                'dial_code' => '+1-684',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Samoa Americana","pt":"Samoa Americana","nl":"Amerikaans Samoa","hr":"Američka Samoa","fa":"ساموآی آمریکا","de":"Amerikanisch-Samoa","es":"Samoa Americana","fr":"Samoa américaines","ja":"アメリカ領サモア","it":"Samoa Americane","ar":"ساموا الأمريكية"}',
                'latitude' => '-14.33333333',
                'longitude' => '-170.00000000',
            ),
            5 =>
            array(
                'name' => 'Andorra',
                'iso3' => 'AND',
                'iso2' => 'AD',
                'dial_code' => '376',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Andorra","pt":"Andorra","nl":"Andorra","hr":"Andora","fa":"آندورا","de":"Andorra","es":"Andorra","fr":"Andorre","ja":"アンドラ","it":"Andorra","ar":"أندورا"}',
                'latitude' => '42.50000000',
                'longitude' => '1.50000000',
            ),
            6 =>
            array(
                'name' => 'Angola',
                'iso3' => 'AGO',
                'iso2' => 'AO',
                'dial_code' => '244',
                'currency' => 'AOA',
                'currency_symbol' => 'Kz',
                'translations' => '{"br":"Angola","pt":"Angola","nl":"Angola","hr":"Angola","fa":"آنگولا","de":"Angola","es":"Angola","fr":"Angola","ja":"アンゴラ","it":"Angola","ar":"أنغولا"}',
                'latitude' => '-12.50000000',
                'longitude' => '18.50000000',
            ),
            7 =>
            array(
                'name' => 'Anguilla',
                'iso3' => 'AIA',
                'iso2' => 'AI',
                'dial_code' => '+1-264',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Anguila","pt":"Anguila","nl":"Anguilla","hr":"Angvila","fa":"آنگویلا","de":"Anguilla","es":"Anguilla","fr":"Anguilla","ja":"アンギラ","it":"Anguilla","ar":"أنغويلا"}',
                'latitude' => '18.25000000',
                'longitude' => '-63.16666666',
            ),
            8 =>
            array(
                'name' => 'Antarctica',
                'iso3' => 'ATA',
                'iso2' => 'AQ',
                'dial_code' => '',
                'currency' => '',
                'currency_symbol' => '$',
                'translations' => '{"br":"Antártida","pt":"Antárctida","nl":"Antarctica","hr":"Antarktika","fa":"جنوبگان","de":"Antarktika","es":"Antártida","fr":"Antarctique","ja":"南極大陸","it":"Antartide","ar":"القارة القطبية الجنوبية"}',
                'latitude' => '-74.65000000',
                'longitude' => '4.48000000',
            ),
            9 =>
            array(
                'name' => 'Antigua And Barbuda',
                'iso3' => 'ATG',
                'iso2' => 'AG',
                'dial_code' => '+1-268',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Antígua e Barbuda","pt":"Antígua e Barbuda","nl":"Antigua en Barbuda","hr":"Antigva i Barbuda","fa":"آنتیگوا و باربودا","de":"Antigua und Barbuda","es":"Antigua y Barbuda","fr":"Antigua-et-Barbuda","ja":"アンティグア・バーブーダ","it":"Antigua e Barbuda","ar":"أنتيغوا وبربودا"}',
                'latitude' => '17.05000000',
                'longitude' => '-61.80000000',
            ),
            10 =>
            array(
                'name' => 'Argentina',
                'iso3' => 'ARG',
                'iso2' => 'AR',
                'dial_code' => '54',
                'currency' => 'ARS',
                'currency_symbol' => '$',
                'translations' => '{"br":"Argentina","pt":"Argentina","nl":"Argentinië","hr":"Argentina","fa":"آرژانتین","de":"Argentinien","es":"Argentina","fr":"Argentine","ja":"アルゼンチン","it":"Argentina","ar":"الأرجنتين"}',
                'latitude' => '-34.00000000',
                'longitude' => '-64.00000000',
            ),
            11 =>
            array(
                'name' => 'Armenia',
                'iso3' => 'ARM',
                'iso2' => 'AM',
                'dial_code' => '374',
                'currency' => 'AMD',
                'currency_symbol' => '֏',
                'translations' => '{"br":"Armênia","pt":"Arménia","nl":"Armenië","hr":"Armenija","fa":"ارمنستان","de":"Armenien","es":"Armenia","fr":"Arménie","ja":"アルメニア","it":"Armenia","ar":"أرمينيا"}',
                'latitude' => '40.00000000',
                'longitude' => '45.00000000',
            ),
            12 =>
            array(
                'name' => 'Aruba',
                'iso3' => 'ABW',
                'iso2' => 'AW',
                'dial_code' => '297',
                'currency' => 'AWG',
                'currency_symbol' => 'ƒ',
                'translations' => '{"br":"Aruba","pt":"Aruba","nl":"Aruba","hr":"Aruba","fa":"آروبا","de":"Aruba","es":"Aruba","fr":"Aruba","ja":"アルバ","it":"Aruba","ar":"أروبا"}',
                'latitude' => '12.50000000',
                'longitude' => '-69.96666666',
            ),
            13 =>
            array(
                'name' => 'Australia',
                'iso3' => 'AUS',
                'iso2' => 'AU',
                'dial_code' => '61',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Austrália","pt":"Austrália","nl":"Australië","hr":"Australija","fa":"استرالیا","de":"Australien","es":"Australia","fr":"Australie","ja":"オーストラリア","it":"Australia","ar":"أستراليا"}',
                'latitude' => '-27.00000000',
                'longitude' => '133.00000000',
            ),
            14 =>
            array(
                'name' => 'Austria',
                'iso3' => 'AUT',
                'iso2' => 'AT',
                'dial_code' => '43',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"áustria","pt":"áustria","nl":"Oostenrijk","hr":"Austrija","fa":"اتریش","de":"Österreich","es":"Austria","fr":"Autriche","ja":"オーストリア","it":"Austria","ar":"النمسا"}',
                'latitude' => '47.33333333',
                'longitude' => '13.33333333',
            ),
            15 =>
            array(
                'name' => 'Azerbaijan',
                'iso3' => 'AZE',
                'iso2' => 'AZ',
                'dial_code' => '994',
                'currency' => 'AZN',
                'currency_symbol' => 'm',
                'translations' => '{"br":"Azerbaijão","pt":"Azerbaijão","nl":"Azerbeidzjan","hr":"Azerbajdžan","fa":"آذربایجان","de":"Aserbaidschan","es":"Azerbaiyán","fr":"Azerbaïdjan","ja":"アゼルバイジャン","it":"Azerbaijan","ar":"أذربيجان"}',
                'latitude' => '40.50000000',
                'longitude' => '47.50000000',
            ),
            16 =>
            array(
                'name' => 'Bahamas The',
                'iso3' => 'BHS',
                'iso2' => 'BS',
                'dial_code' => '+1-242',
                'currency' => 'BSD',
                'currency_symbol' => 'B$',
                'translations' => '{"br":"Bahamas","pt":"Baamas","nl":"Bahama’s","hr":"Bahami","fa":"باهاما","de":"Bahamas","es":"Bahamas","fr":"Bahamas","ja":"バハマ","it":"Bahamas","ar":"جزر البهاما"}',
                'latitude' => '24.25000000',
                'longitude' => '-76.00000000',
            ),
            17 =>
            array(
                'name' => 'Bahrain',
                'iso3' => 'BHR',
                'iso2' => 'BH',
                'dial_code' => '973',
                'currency' => 'BHD',
                'currency_symbol' => '.د.ب',
                'translations' => '{"br":"Bahrein","pt":"Barém","nl":"Bahrein","hr":"Bahrein","fa":"بحرین","de":"Bahrain","es":"Bahrein","fr":"Bahreïn","ja":"バーレーン","it":"Bahrein","ar":"البحرين"}',
                'latitude' => '26.00000000',
                'longitude' => '50.55000000',
            ),
            18 =>
            array(
                'name' => 'Bangladesh',
                'iso3' => 'BGD',
                'iso2' => 'BD',
                'dial_code' => '880',
                'currency' => 'BDT',
                'currency_symbol' => '৳',
                'translations' => '{"br":"Bangladesh","pt":"Bangladeche","nl":"Bangladesh","hr":"Bangladeš","fa":"بنگلادش","de":"Bangladesch","es":"Bangladesh","fr":"Bangladesh","ja":"バングラデシュ","it":"Bangladesh","ar":"بنغلاديش"}',
                'latitude' => '24.00000000',
                'longitude' => '90.00000000',
            ),
            19 =>
            array(
                'name' => 'Barbados',
                'iso3' => 'BRB',
                'iso2' => 'BB',
                'dial_code' => '+1-246',
                'currency' => 'BBD',
                'currency_symbol' => 'Bds$',
                'translations' => '{"br":"Barbados","pt":"Barbados","nl":"Barbados","hr":"Barbados","fa":"باربادوس","de":"Barbados","es":"Barbados","fr":"Barbade","ja":"バルバドス","it":"Barbados","ar":"بربادوس"}',
                'latitude' => '13.16666666',
                'longitude' => '-59.53333333',
            ),
            20 =>
            array(
                'name' => 'Belarus',
                'iso3' => 'BLR',
                'iso2' => 'BY',
                'dial_code' => '375',
                'currency' => 'BYN',
                'currency_symbol' => 'Br',
                'translations' => '{"br":"Bielorrússia","pt":"Bielorrússia","nl":"Wit-Rusland","hr":"Bjelorusija","fa":"بلاروس","de":"Weißrussland","es":"Bielorrusia","fr":"Biélorussie","ja":"ベラルーシ","it":"Bielorussia","ar":"بيلاروس"}',
                'latitude' => '53.00000000',
                'longitude' => '28.00000000',
            ),
            21 =>
            array(
                'name' => 'Belgium',
                'iso3' => 'BEL',
                'iso2' => 'BE',
                'dial_code' => '32',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Bélgica","pt":"Bélgica","nl":"België","hr":"Belgija","fa":"بلژیک","de":"Belgien","es":"Bélgica","fr":"Belgique","ja":"ベルギー","it":"Belgio","ar":"بلجيكا"}',
                'latitude' => '50.83333333',
                'longitude' => '4.00000000',
            ),
            22 =>
            array(
                'name' => 'Belize',
                'iso3' => 'BLZ',
                'iso2' => 'BZ',
                'dial_code' => '501',
                'currency' => 'BZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Belize","pt":"Belize","nl":"Belize","hr":"Belize","fa":"بلیز","de":"Belize","es":"Belice","fr":"Belize","ja":"ベリーズ","it":"Belize","ar":"بليز"}',
                'latitude' => '17.25000000',
                'longitude' => '-88.75000000',
            ),
            23 =>
            array(
                'name' => 'Benin',
                'iso3' => 'BEN',
                'iso2' => 'BJ',
                'dial_code' => '229',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Benin","pt":"Benim","nl":"Benin","hr":"Benin","fa":"بنین","de":"Benin","es":"Benín","fr":"Bénin","ja":"ベナン","it":"Benin","ar":"بنين"}',
                'latitude' => '9.50000000',
                'longitude' => '2.25000000',
            ),
            24 =>
            array(
                'name' => 'Bermuda',
                'iso3' => 'BMU',
                'iso2' => 'BM',
                'dial_code' => '+1-441',
                'currency' => 'BMD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Bermudas","pt":"Bermudas","nl":"Bermuda","hr":"Bermudi","fa":"برمودا","de":"Bermuda","es":"Bermudas","fr":"Bermudes","ja":"バミューダ","it":"Bermuda","ar":"برمودا"}',
                'latitude' => '32.33333333',
                'longitude' => '-64.75000000',
            ),
            25 =>
            array(
                'name' => 'Bhutan',
                'iso3' => 'BTN',
                'iso2' => 'BT',
                'dial_code' => '975',
                'currency' => 'BTN',
                'currency_symbol' => 'Nu.',
                'translations' => '{"br":"Butão","pt":"Butão","nl":"Bhutan","hr":"Butan","fa":"بوتان","de":"Bhutan","es":"Bután","fr":"Bhoutan","ja":"ブータン","it":"Bhutan","ar":"بوتان"}',
                'latitude' => '27.50000000',
                'longitude' => '90.50000000',
            ),
            26 =>
            array(
                'name' => 'Bolivia',
                'iso3' => 'BOL',
                'iso2' => 'BO',
                'dial_code' => '591',
                'currency' => 'BOB',
                'currency_symbol' => 'Bs.',
                'translations' => '{"br":"Bolívia","pt":"Bolívia","nl":"Bolivia","hr":"Bolivija","fa":"بولیوی","de":"Bolivien","es":"Bolivia","fr":"Bolivie","ja":"ボリビア多民族国","it":"Bolivia","ar":"بوليفيا"}',
                'latitude' => '-17.00000000',
                'longitude' => '-65.00000000',
            ),
            27 =>
            array(
                'name' => 'Bosnia and Herzegovina',
                'iso3' => 'BIH',
                'iso2' => 'BA',
                'dial_code' => '387',
                'currency' => 'BAM',
                'currency_symbol' => 'KM',
                'translations' => '{"br":"Bósnia e Herzegovina","pt":"Bósnia e Herzegovina","nl":"Bosnië en Herzegovina","hr":"Bosna i Hercegovina","fa":"بوسنی و هرزگوین","de":"Bosnien und Herzegowina","es":"Bosnia y Herzegovina","fr":"Bosnie-Herzégovine","ja":"ボスニア・ヘルツェゴビナ","it":"Bosnia ed Erzegovina","ar":"البوسنة والهرسك"}',
                'latitude' => '44.00000000',
                'longitude' => '18.00000000',
            ),
            28 =>
            array(
                'name' => 'Botswana',
                'iso3' => 'BWA',
                'iso2' => 'BW',
                'dial_code' => '267',
                'currency' => 'BWP',
                'currency_symbol' => 'P',
                'translations' => '{"br":"Botsuana","pt":"Botsuana","nl":"Botswana","hr":"Bocvana","fa":"بوتسوانا","de":"Botswana","es":"Botswana","fr":"Botswana","ja":"ボツワナ","it":"Botswana","ar":"بوتسوانا"}',
                'latitude' => '-22.00000000',
                'longitude' => '24.00000000',
            ),
            29 =>
            array(
                'name' => 'Bouvet Island',
                'iso3' => 'BVT',
                'iso2' => 'BV',
                'dial_code' => '0055',
                'currency' => 'NOK',
                'currency_symbol' => 'kr',
                'translations' => '{"br":"Ilha Bouvet","pt":"Ilha Bouvet","nl":"Bouveteiland","hr":"Otok Bouvet","fa":"جزیره بووه","de":"Bouvetinsel","es":"Isla Bouvet","fr":"Île Bouvet","ja":"ブーベ島","it":"Isola Bouvet","ar":"جزيرة بوفيت"}',
                'latitude' => '-54.43333333',
                'longitude' => '3.40000000',
            ),
            30 =>
            array(
                'name' => 'Brazil',
                'iso3' => 'BRA',
                'iso2' => 'BR',
                'dial_code' => '55',
                'currency' => 'BRL',
                'currency_symbol' => 'R$',
                'translations' => '{"br":"Brasil","pt":"Brasil","nl":"Brazilië","hr":"Brazil","fa":"برزیل","de":"Brasilien","es":"Brasil","fr":"Brésil","ja":"ブラジル","it":"Brasile","ar":"البرازيل"}',
                'latitude' => '-10.00000000',
                'longitude' => '-55.00000000',
            ),
            31 =>
            array(
                'name' => 'British Indian Ocean Territory',
                'iso3' => 'IOT',
                'iso2' => 'IO',
                'dial_code' => '246',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Território Britânico do Oceano íÍdico","pt":"Território Britânico do Oceano Índico","nl":"Britse Gebieden in de Indische Oceaan","hr":"Britanski Indijskooceanski teritorij","fa":"قلمرو بریتانیا در اقیانوس هند","de":"Britisches Territorium im Indischen Ozean","es":"Territorio Británico del Océano Índico","fr":"Territoire britannique de l\'océan Indien","ja":"イギリス領インド洋地域","it":"Territorio britannico dell\'oceano indiano","ar":"إقليم المحيط الهندي البريطاني"}',
                'latitude' => '-6.00000000',
                'longitude' => '71.50000000',
            ),
            32 =>
            array(
                'name' => 'Brunei',
                'iso3' => 'BRN',
                'iso2' => 'BN',
                'dial_code' => '673',
                'currency' => 'BND',
                'currency_symbol' => 'B$',
                'translations' => '{"br":"Brunei","pt":"Brunei","nl":"Brunei","hr":"Brunej","fa":"برونئی","de":"Brunei","es":"Brunei","fr":"Brunei","ja":"ブルネイ・ダルサラーム","it":"Brunei","ar":"بروناي"}',
                'latitude' => '4.50000000',
                'longitude' => '114.66666666',
            ),
            33 =>
            array(
                'name' => 'Bulgaria',
                'iso3' => 'BGR',
                'iso2' => 'BG',
                'dial_code' => '359',
                'currency' => 'BGN',
                'currency_symbol' => 'Лв.',
                'translations' => '{"br":"Bulgária","pt":"Bulgária","nl":"Bulgarije","hr":"Bugarska","fa":"بلغارستان","de":"Bulgarien","es":"Bulgaria","fr":"Bulgarie","ja":"ブルガリア","it":"Bulgaria","ar":"بلغاريا"}',
                'latitude' => '43.00000000',
                'longitude' => '25.00000000',
            ),
            34 =>
            array(
                'name' => 'Burkina Faso',
                'iso3' => 'BFA',
                'iso2' => 'BF',
                'dial_code' => '226',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Burkina Faso","pt":"Burquina Faso","nl":"Burkina Faso","hr":"Burkina Faso","fa":"بورکینافاسو","de":"Burkina Faso","es":"Burkina Faso","fr":"Burkina Faso","ja":"ブルキナファソ","it":"Burkina Faso","ar":"بوركينا فاسو"}',
                'latitude' => '13.00000000',
                'longitude' => '-2.00000000',
            ),
            35 =>
            array(
                'name' => 'Burundi',
                'iso3' => 'BDI',
                'iso2' => 'BI',
                'dial_code' => '257',
                'currency' => 'BIF',
                'currency_symbol' => 'FBu',
                'translations' => '{"br":"Burundi","pt":"Burúndi","nl":"Burundi","hr":"Burundi","fa":"بوروندی","de":"Burundi","es":"Burundi","fr":"Burundi","ja":"ブルンジ","it":"Burundi","ar":"بوروندي"}',
                'latitude' => '-3.50000000',
                'longitude' => '30.00000000',
            ),
            36 =>
            array(
                'name' => 'Cambodia',
                'iso3' => 'KHM',
                'iso2' => 'KH',
                'dial_code' => '855',
                'currency' => 'KHR',
                'currency_symbol' => 'KHR',
                'translations' => '{"br":"Camboja","pt":"Camboja","nl":"Cambodja","hr":"Kambodža","fa":"کامبوج","de":"Kambodscha","es":"Camboya","fr":"Cambodge","ja":"カンボジア","it":"Cambogia","ar":"كمبوديا"}',
                'latitude' => '13.00000000',
                'longitude' => '105.00000000',
            ),
            37 =>
            array(
                'name' => 'Cameroon',
                'iso3' => 'CMR',
                'iso2' => 'CM',
                'dial_code' => '237',
                'currency' => 'XAF',
                'currency_symbol' => 'FCFA',
                'translations' => '{"br":"Camarões","pt":"Camarões","nl":"Kameroen","hr":"Kamerun","fa":"کامرون","de":"Kamerun","es":"Camerún","fr":"Cameroun","ja":"カメルーン","it":"Camerun","ar":"الكاميرون"}',
                'latitude' => '6.00000000',
                'longitude' => '12.00000000',
            ),
            38 =>
            array(
                'name' => 'Canada',
                'iso3' => 'CAN',
                'iso2' => 'CA',
                'dial_code' => '1',
                'currency' => 'CAD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Canadá","pt":"Canadá","nl":"Canada","hr":"Kanada","fa":"کانادا","de":"Kanada","es":"Canadá","fr":"Canada","ja":"カナダ","it":"Canada","ar":"كندا"}',
                'latitude' => '60.00000000',
                'longitude' => '-95.00000000',
            ),
            39 =>
            array(
                'name' => 'Cape Verde',
                'iso3' => 'CPV',
                'iso2' => 'CV',
                'dial_code' => '238',
                'currency' => 'CVE',
                'currency_symbol' => '$',
                'translations' => '{"br":"Cabo Verde","pt":"Cabo Verde","nl":"Kaapverdië","hr":"Zelenortska Republika","fa":"کیپ ورد","de":"Kap Verde","es":"Cabo Verde","fr":"Cap Vert","ja":"カーボベルデ","it":"Capo Verde","ar":"الرأس الأخضر"}',
                'latitude' => '16.00000000',
                'longitude' => '-24.00000000',
            ),
            40 =>
            array(
                'name' => 'Cayman Islands',
                'iso3' => 'CYM',
                'iso2' => 'KY',
                'dial_code' => '+1-345',
                'currency' => 'KYD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Cayman","pt":"Ilhas Caimão","nl":"Caymaneilanden","hr":"Kajmanski otoci","fa":"جزایر کیمن","de":"Kaimaninseln","es":"Islas Caimán","fr":"Îles Caïmans","ja":"ケイマン諸島","it":"Isole Cayman","ar":"جزر كايمان"}',
                'latitude' => '19.50000000',
                'longitude' => '-80.50000000',
            ),
            41 =>
            array(
                'name' => 'Central African Republic',
                'iso3' => 'CAF',
                'iso2' => 'CF',
                'dial_code' => '236',
                'currency' => 'XAF',
                'currency_symbol' => 'FCFA',
                'translations' => '{"br":"República Centro-Africana","pt":"República Centro-Africana","nl":"Centraal-Afrikaanse Republiek","hr":"Srednjoafrička Republika","fa":"جمهوری آفریقای مرکزی","de":"Zentralafrikanische Republik","es":"República Centroafricana","fr":"République centrafricaine","ja":"中央アフリカ共和国","it":"Repubblica Centrafricana","ar":"جمهورية أفريقيا الوسطى"}',
                'latitude' => '7.00000000',
                'longitude' => '21.00000000',
            ),
            42 =>
            array(
                'name' => 'Chad',
                'iso3' => 'TCD',
                'iso2' => 'TD',
                'dial_code' => '235',
                'currency' => 'XAF',
                'currency_symbol' => 'FCFA',
                'translations' => '{"br":"Chade","pt":"Chade","nl":"Tsjaad","hr":"Čad","fa":"چاد","de":"Tschad","es":"Chad","fr":"Tchad","ja":"チャド","it":"Ciad","ar":"تشاد"}',
                'latitude' => '15.00000000',
                'longitude' => '19.00000000',
            ),
            43 =>
            array(
                'name' => 'Chile',
                'iso3' => 'CHL',
                'iso2' => 'CL',
                'dial_code' => '56',
                'currency' => 'CLP',
                'currency_symbol' => '$',
                'translations' => '{"br":"Chile","pt":"Chile","nl":"Chili","hr":"Čile","fa":"شیلی","de":"Chile","es":"Chile","fr":"Chili","ja":"チリ","it":"Cile","ar":"تشيلي"}',
                'latitude' => '-30.00000000',
                'longitude' => '-71.00000000',
            ),
            44 =>
            array(
                'name' => 'China',
                'iso3' => 'CHN',
                'iso2' => 'CN',
                'dial_code' => '86',
                'currency' => 'CNY',
                'currency_symbol' => '¥',
                'translations' => '{"br":"China","pt":"China","nl":"China","hr":"Kina","fa":"چین","de":"China","es":"China","fr":"Chine","ja":"中国","it":"Cina","ar":"الصين"}',
                'latitude' => '35.00000000',
                'longitude' => '105.00000000',
            ),
            45 =>
            array(
                'name' => 'Christmas Island',
                'iso3' => 'CXR',
                'iso2' => 'CX',
                'dial_code' => '61',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilha Christmas","pt":"Ilha do Natal","nl":"Christmaseiland","hr":"Božićni otok","fa":"جزیره کریسمس","de":"Weihnachtsinsel","es":"Isla de Navidad","fr":"Île Christmas","ja":"クリスマス島","it":"Isola di Natale","ar":"جزيرة الكريسماس"}',
                'latitude' => '-10.50000000',
                'longitude' => '105.66666666',
            ),
            46 =>
            array(
                'name' => 'Cocos (Keeling) Islands',
                'iso3' => 'CCK',
                'iso2' => 'CC',
                'dial_code' => '61',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Cocos","pt":"Ilhas dos Cocos","nl":"Cocoseilanden","hr":"Kokosovi Otoci","fa":"جزایر کوکوس","de":"Kokosinseln","es":"Islas Cocos o Islas Keeling","fr":"Îles Cocos","ja":"ココス（キーリング）諸島","it":"Isole Cocos e Keeling","ar":"جزر كوكوس"}',
                'latitude' => '-12.50000000',
                'longitude' => '96.83333333',
            ),
            47 =>
            array(
                'name' => 'Colombia',
                'iso3' => 'COL',
                'iso2' => 'CO',
                'dial_code' => '57',
                'currency' => 'COP',
                'currency_symbol' => '$',
                'translations' => '{"br":"Colômbia","pt":"Colômbia","nl":"Colombia","hr":"Kolumbija","fa":"کلمبیا","de":"Kolumbien","es":"Colombia","fr":"Colombie","ja":"コロンビア","it":"Colombia","ar":"كولومبيا"}',
                'latitude' => '4.00000000',
                'longitude' => '-72.00000000',
            ),
            48 =>
            array(
                'name' => 'Comoros',
                'iso3' => 'COM',
                'iso2' => 'KM',
                'dial_code' => '269',
                'currency' => 'KMF',
                'currency_symbol' => 'CF',
                'translations' => '{"br":"Comores","pt":"Comores","nl":"Comoren","hr":"Komori","fa":"کومور","de":"Union der Komoren","es":"Comoras","fr":"Comores","ja":"コモロ","it":"Comore","ar":"جزر القمر"}',
                'latitude' => '-12.16666666',
                'longitude' => '44.25000000',
            ),
            49 =>
            array(
                'name' => 'Congo',
                'iso3' => 'COG',
                'iso2' => 'CG',
                'dial_code' => '242',
                'currency' => 'XAF',
                'currency_symbol' => 'FC',
                'translations' => '{"br":"Congo","pt":"Congo","nl":"Congo [Republiek]","hr":"Kongo","fa":"کنگو","de":"Kongo","es":"Congo","fr":"Congo","ja":"コンゴ共和国","it":"Congo","ar":"الكونغو"}',
                'latitude' => '-1.00000000',
                'longitude' => '15.00000000',
            ),
            50 =>
            array(
                'name' => 'Congo The Democratic Republic Of The',
                'iso3' => 'COD',
                'iso2' => 'CD',
                'dial_code' => '243',
                'currency' => 'CDF',
                'currency_symbol' => 'FC',
                'translations' => '{"br":"RD Congo","pt":"RD Congo","nl":"Congo [DRC]","hr":"Kongo, Demokratska Republika","fa":"جمهوری کنگو","de":"Kongo (Dem. Rep.)","es":"Congo (Rep. Dem.)","fr":"Congo (Rép. dém.)","ja":"コンゴ民主共和国","it":"Congo (Rep. Dem.)","ar":"جمهورية الكونغو الديمقراطية"}',
                'latitude' => '0.00000000',
                'longitude' => '25.00000000',
            ),
            51 =>
            array(
                'name' => 'Cook Islands',
                'iso3' => 'COK',
                'iso2' => 'CK',
                'dial_code' => '682',
                'currency' => 'NZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Cook","pt":"Ilhas Cook","nl":"Cookeilanden","hr":"Cookovo Otočje","fa":"جزایر کوک","de":"Cookinseln","es":"Islas Cook","fr":"Îles Cook","ja":"クック諸島","it":"Isole Cook","ar":"جزر كوك"}',
                'latitude' => '-21.23333333',
                'longitude' => '-159.76666666',
            ),
            52 =>
            array(
                'name' => 'Costa Rica',
                'iso3' => 'CRI',
                'iso2' => 'CR',
                'dial_code' => '506',
                'currency' => 'CRC',
                'currency_symbol' => '₡',
                'translations' => '{"br":"Costa Rica","pt":"Costa Rica","nl":"Costa Rica","hr":"Kostarika","fa":"کاستاریکا","de":"Costa Rica","es":"Costa Rica","fr":"Costa Rica","ja":"コスタリカ","it":"Costa Rica","ar":"كوستاريكا"}',
                'latitude' => '10.00000000',
                'longitude' => '-84.00000000',
            ),
            53 =>
            array(
                'name' => 'Cote D\'Ivoire (Ivory Coast)',
                'iso3' => 'CIV',
                'iso2' => 'CI',
                'dial_code' => '225',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Costa do Marfim","pt":"Costa do Marfim","nl":"Ivoorkust","hr":"Obala Bjelokosti","fa":"ساحل عاج","de":"Elfenbeinküste","es":"Costa de Marfil","fr":"Côte d\'Ivoire","ja":"コートジボワール","it":"Costa D\'Avorio","ar":"ساحل العاج"}',
                'latitude' => '8.00000000',
                'longitude' => '-5.00000000',
            ),
            54 =>
            array(
                'name' => 'Croatia (Hrvatska)',
                'iso3' => 'HRV',
                'iso2' => 'HR',
                'dial_code' => '385',
                'currency' => 'HRK',
                'currency_symbol' => 'kn',
                'translations' => '{"br":"Croácia","pt":"Croácia","nl":"Kroatië","hr":"Hrvatska","fa":"کرواسی","de":"Kroatien","es":"Croacia","fr":"Croatie","ja":"クロアチア","it":"Croazia","ar":"Croatia (Hrvatska)"}',
                'latitude' => '45.16666666',
                'longitude' => '15.50000000',
            ),
            55 =>
            array(
                'name' => 'Cuba',
                'iso3' => 'CUB',
                'iso2' => 'CU',
                'dial_code' => '53',
                'currency' => 'CUP',
                'currency_symbol' => '$',
                'translations' => '{"br":"Cuba","pt":"Cuba","nl":"Cuba","hr":"Kuba","fa":"کوبا","de":"Kuba","es":"Cuba","fr":"Cuba","ja":"キューバ","it":"Cuba","ar":"كوبا"}',
                'latitude' => '21.50000000',
                'longitude' => '-80.00000000',
            ),
            56 =>
            array(
                'name' => 'Cyprus',
                'iso3' => 'CYP',
                'iso2' => 'CY',
                'dial_code' => '357',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Chipre","pt":"Chipre","nl":"Cyprus","hr":"Cipar","fa":"قبرس","de":"Zypern","es":"Chipre","fr":"Chypre","ja":"キプロス","it":"Cipro","ar":"قبرص"}',
                'latitude' => '35.00000000',
                'longitude' => '33.00000000',
            ),
            57 =>
            array(
                'name' => 'Czech Republic',
                'iso3' => 'CZE',
                'iso2' => 'CZ',
                'dial_code' => '420',
                'currency' => 'CZK',
                'currency_symbol' => 'Kč',
                'translations' => '{"br":"República Tcheca","pt":"República Checa","nl":"Tsjechië","hr":"Češka","fa":"جمهوری چک","de":"Tschechische Republik","es":"República Checa","fr":"République tchèque","ja":"チェコ","it":"Repubblica Ceca","ar":"جمهورية التشيك"}',
                'latitude' => '49.75000000',
                'longitude' => '15.50000000',
            ),
            58 =>
            array(
                'name' => 'Denmark',
                'iso3' => 'DNK',
                'iso2' => 'DK',
                'dial_code' => '45',
                'currency' => 'DKK',
                'currency_symbol' => 'Kr.',
                'translations' => '{"br":"Dinamarca","pt":"Dinamarca","nl":"Denemarken","hr":"Danska","fa":"دانمارک","de":"Dänemark","es":"Dinamarca","fr":"Danemark","ja":"デンマーク","it":"Danimarca","ar":"الدنمارك"}',
                'latitude' => '56.00000000',
                'longitude' => '10.00000000',
            ),
            59 =>
            array(
                'name' => 'Djibouti',
                'iso3' => 'DJI',
                'iso2' => 'DJ',
                'dial_code' => '253',
                'currency' => 'DJF',
                'currency_symbol' => 'Fdj',
                'translations' => '{"br":"Djibuti","pt":"Djibuti","nl":"Djibouti","hr":"Džibuti","fa":"جیبوتی","de":"Dschibuti","es":"Yibuti","fr":"Djibouti","ja":"ジブチ","it":"Gibuti","ar":"جيبوتي"}',
                'latitude' => '11.50000000',
                'longitude' => '43.00000000',
            ),
            60 =>
            array(
                'name' => 'Dominica',
                'iso3' => 'DMA',
                'iso2' => 'DM',
                'dial_code' => '+1-767',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Dominica","pt":"Dominica","nl":"Dominica","hr":"Dominika","fa":"دومینیکا","de":"Dominica","es":"Dominica","fr":"Dominique","ja":"ドミニカ国","it":"Dominica","ar":"دومينيكا"}',
                'latitude' => '15.41666666',
                'longitude' => '-61.33333333',
            ),
            61 =>
            array(
                'name' => 'Dominican Republic',
                'iso3' => 'DOM',
                'iso2' => 'DO',
                'dial_code' => '+1-809 and 1-829',
                'currency' => 'DOP',
                'currency_symbol' => '$',
                'translations' => '{"br":"República Dominicana","pt":"República Dominicana","nl":"Dominicaanse Republiek","hr":"Dominikanska Republika","fa":"جمهوری دومینیکن","de":"Dominikanische Republik","es":"República Dominicana","fr":"République dominicaine","ja":"ドミニカ共和国","it":"Repubblica Dominicana","ar":"جمهورية الدومينيكان"}',
                'latitude' => '19.00000000',
                'longitude' => '-70.66666666',
            ),
            62 =>
            array(
                'name' => 'East Timor',
                'iso3' => 'TLS',
                'iso2' => 'TL',
                'dial_code' => '670',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Timor Leste","pt":"Timor Leste","nl":"Oost-Timor","hr":"Istočni Timor","fa":"تیمور شرقی","de":"Timor-Leste","es":"Timor Oriental","fr":"Timor oriental","ja":"東ティモール","it":"Timor Est","ar":"تيمور الشرقية"}',
                'latitude' => '-8.83333333',
                'longitude' => '125.91666666',
            ),
            63 =>
            array(
                'name' => 'Ecuador',
                'iso3' => 'ECU',
                'iso2' => 'EC',
                'dial_code' => '593',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Equador","pt":"Equador","nl":"Ecuador","hr":"Ekvador","fa":"اکوادور","de":"Ecuador","es":"Ecuador","fr":"Équateur","ja":"エクアドル","it":"Ecuador","ar":"الإكوادور"}',
                'latitude' => '-2.00000000',
                'longitude' => '-77.50000000',
            ),
            64 =>
            array(
                'name' => 'Egypt',
                'iso3' => 'EGY',
                'iso2' => 'EG',
                'dial_code' => '20',
                'currency' => 'EGP',
                'currency_symbol' => 'ج.م',
                'translations' => '{"br":"Egito","pt":"Egipto","nl":"Egypte","hr":"Egipat","fa":"مصر","de":"Ägypten","es":"Egipto","fr":"Égypte","ja":"エジプト","it":"Egitto","ar":"مصر"}',
                'latitude' => '27.00000000',
                'longitude' => '30.00000000',
            ),
            65 =>
            array(
                'name' => 'El Salvador',
                'iso3' => 'SLV',
                'iso2' => 'SV',
                'dial_code' => '503',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"El Salvador","pt":"El Salvador","nl":"El Salvador","hr":"Salvador","fa":"السالوادور","de":"El Salvador","es":"El Salvador","fr":"Salvador","ja":"エルサルバドル","it":"El Salvador","ar":"السلفادور"}',
                'latitude' => '13.83333333',
                'longitude' => '-88.91666666',
            ),
            66 =>
            array(
                'name' => 'Equatorial Guinea',
                'iso3' => 'GNQ',
                'iso2' => 'GQ',
                'dial_code' => '240',
                'currency' => 'XAF',
                'currency_symbol' => 'FCFA',
                'translations' => '{"br":"Guiné Equatorial","pt":"Guiné Equatorial","nl":"Equatoriaal-Guinea","hr":"Ekvatorijalna Gvineja","fa":"گینه استوایی","de":"Äquatorial-Guinea","es":"Guinea Ecuatorial","fr":"Guinée-Équatoriale","ja":"赤道ギニア","it":"Guinea Equatoriale","ar":"غينيا الاستوائية"}',
                'latitude' => '2.00000000',
                'longitude' => '10.00000000',
            ),
            67 =>
            array(
                'name' => 'Eritrea',
                'iso3' => 'ERI',
                'iso2' => 'ER',
                'dial_code' => '291',
                'currency' => 'ERN',
                'currency_symbol' => 'Nfk',
                'translations' => '{"br":"Eritreia","pt":"Eritreia","nl":"Eritrea","hr":"Eritreja","fa":"اریتره","de":"Eritrea","es":"Eritrea","fr":"Érythrée","ja":"エリトリア","it":"Eritrea","ar":"إريتريا"}',
                'latitude' => '15.00000000',
                'longitude' => '39.00000000',
            ),
            68 =>
            array(
                'name' => 'Estonia',
                'iso3' => 'EST',
                'iso2' => 'EE',
                'dial_code' => '372',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Estônia","pt":"Estónia","nl":"Estland","hr":"Estonija","fa":"استونی","de":"Estland","es":"Estonia","fr":"Estonie","ja":"エストニア","it":"Estonia","ar":"إستونيا"}',
                'latitude' => '59.00000000',
                'longitude' => '26.00000000',
            ),
            69 =>
            array(
                'name' => 'Ethiopia',
                'iso3' => 'ETH',
                'iso2' => 'ET',
                'dial_code' => '251',
                'currency' => 'ETB',
                'currency_symbol' => 'Nkf',
                'translations' => '{"br":"Etiópia","pt":"Etiópia","nl":"Ethiopië","hr":"Etiopija","fa":"اتیوپی","de":"Äthiopien","es":"Etiopía","fr":"Éthiopie","ja":"エチオピア","it":"Etiopia","ar":"إثيوبيا"}',
                'latitude' => '8.00000000',
                'longitude' => '38.00000000',
            ),
            70 =>
            array(
                'name' => 'Falkland Islands',
                'iso3' => 'FLK',
                'iso2' => 'FK',
                'dial_code' => '500',
                'currency' => 'FKP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Ilhas Malvinas","pt":"Ilhas Falkland","nl":"Falklandeilanden [Islas Malvinas]","hr":"Falklandski Otoci","fa":"جزایر فالکلند","de":"Falklandinseln","es":"Islas Malvinas","fr":"Îles Malouines","ja":"フォークランド（マルビナス）諸島","it":"Isole Falkland o Isole Malvine","ar":"جزر فوكلاند"}',
                'latitude' => '-51.75000000',
                'longitude' => '-59.00000000',
            ),
            71 =>
            array(
                'name' => 'Faroe Islands',
                'iso3' => 'FRO',
                'iso2' => 'FO',
                'dial_code' => '298',
                'currency' => 'DKK',
                'currency_symbol' => 'Kr.',
                'translations' => '{"br":"Ilhas Faroé","pt":"Ilhas Faroé","nl":"Faeröer","hr":"Farski Otoci","fa":"جزایر فارو","de":"Färöer-Inseln","es":"Islas Faroe","fr":"Îles Féroé","ja":"フェロー諸島","it":"Isole Far Oer","ar":"جزر فارو"}',
                'latitude' => '62.00000000',
                'longitude' => '-7.00000000',
            ),
            72 =>
            array(
                'name' => 'Fiji Islands',
                'iso3' => 'FJI',
                'iso2' => 'FJ',
                'dial_code' => '679',
                'currency' => 'FJD',
                'currency_symbol' => 'FJ$',
                'translations' => '{"br":"Fiji","pt":"Fiji","nl":"Fiji","hr":"Fiđi","fa":"فیجی","de":"Fidschi","es":"Fiyi","fr":"Fidji","ja":"フィジー","it":"Figi","ar":"جزر فيجي"}',
                'latitude' => '-18.00000000',
                'longitude' => '175.00000000',
            ),
            73 =>
            array(
                'name' => 'Finland',
                'iso3' => 'FIN',
                'iso2' => 'FI',
                'dial_code' => '358',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Finlândia","pt":"Finlândia","nl":"Finland","hr":"Finska","fa":"فنلاند","de":"Finnland","es":"Finlandia","fr":"Finlande","ja":"フィンランド","it":"Finlandia","ar":"فنلندا"}',
                'latitude' => '64.00000000',
                'longitude' => '26.00000000',
            ),
            74 =>
            array(
                'name' => 'France',
                'iso3' => 'FRA',
                'iso2' => 'FR',
                'dial_code' => '33',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"França","pt":"França","nl":"Frankrijk","hr":"Francuska","fa":"فرانسه","de":"Frankreich","es":"Francia","fr":"France","ja":"フランス","it":"Francia","ar":"فرنسا"}',
                'latitude' => '46.00000000',
                'longitude' => '2.00000000',
            ),
            75 =>
            array(
                'name' => 'French Guiana',
                'iso3' => 'GUF',
                'iso2' => 'GF',
                'dial_code' => '594',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Guiana Francesa","pt":"Guiana Francesa","nl":"Frans-Guyana","hr":"Francuska Gvajana","fa":"گویان فرانسه","de":"Französisch Guyana","es":"Guayana Francesa","fr":"Guayane","ja":"フランス領ギアナ","it":"Guyana francese","ar":"غويانا الفرنسية"}',
                'latitude' => '4.00000000',
                'longitude' => '-53.00000000',
            ),
            76 =>
            array(
                'name' => 'French Polynesia',
                'iso3' => 'PYF',
                'iso2' => 'PF',
                'dial_code' => '689',
                'currency' => 'XPF',
                'currency_symbol' => '₣',
                'translations' => '{"br":"Polinésia Francesa","pt":"Polinésia Francesa","nl":"Frans-Polynesië","hr":"Francuska Polinezija","fa":"پلی‌نزی فرانسه","de":"Französisch-Polynesien","es":"Polinesia Francesa","fr":"Polynésie française","ja":"フランス領ポリネシア","it":"Polinesia Francese","ar":"بولينيزيا الفرنسية"}',
                'latitude' => '-15.00000000',
                'longitude' => '-140.00000000',
            ),
            77 =>
            array(
                'name' => 'French Southern Territories',
                'iso3' => 'ATF',
                'iso2' => 'TF',
                'dial_code' => '',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Terras Austrais e Antárticas Francesas","pt":"Terras Austrais e Antárticas Francesas","nl":"Franse Gebieden in de zuidelijke Indische Oceaan","hr":"Francuski južni i antarktički teritoriji","fa":"سرزمین‌های جنوبی و جنوبگانی فرانسه","de":"Französische Süd- und Antarktisgebiete","es":"Tierras Australes y Antárticas Francesas","fr":"Terres australes et antarctiques françaises","ja":"フランス領南方・南極地域","it":"Territori Francesi del Sud","ar":"الأقاليم الجنوبية الفرنسية"}',
                'latitude' => '-49.25000000',
                'longitude' => '69.16700000',
            ),
            78 =>
            array(
                'name' => 'Gabon',
                'iso3' => 'GAB',
                'iso2' => 'GA',
                'dial_code' => '241',
                'currency' => 'XAF',
                'currency_symbol' => 'FCFA',
                'translations' => '{"br":"Gabão","pt":"Gabão","nl":"Gabon","hr":"Gabon","fa":"گابن","de":"Gabun","es":"Gabón","fr":"Gabon","ja":"ガボン","it":"Gabon","ar":"الغابون"}',
                'latitude' => '-1.00000000',
                'longitude' => '11.75000000',
            ),
            79 =>
            array(
                'name' => 'Gambia The',
                'iso3' => 'GMB',
                'iso2' => 'GM',
                'dial_code' => '220',
                'currency' => 'GMD',
                'currency_symbol' => 'D',
                'translations' => '{"br":"Gâmbia","pt":"Gâmbia","nl":"Gambia","hr":"Gambija","fa":"گامبیا","de":"Gambia","es":"Gambia","fr":"Gambie","ja":"ガンビア","it":"Gambia","ar":"غامبيا"}',
                'latitude' => '13.46666666',
                'longitude' => '-16.56666666',
            ),
            80 =>
            array(
                'name' => 'Georgia',
                'iso3' => 'GEO',
                'iso2' => 'GE',
                'dial_code' => '995',
                'currency' => 'GEL',
                'currency_symbol' => 'ლ',
                'translations' => '{"br":"Geórgia","pt":"Geórgia","nl":"Georgië","hr":"Gruzija","fa":"گرجستان","de":"Georgien","es":"Georgia","fr":"Géorgie","ja":"グルジア","it":"Georgia","ar":"جورجيا"}',
                'latitude' => '42.00000000',
                'longitude' => '43.50000000',
            ),
            81 =>
            array(
                'name' => 'Germany',
                'iso3' => 'DEU',
                'iso2' => 'DE',
                'dial_code' => '49',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Alemanha","pt":"Alemanha","nl":"Duitsland","hr":"Njemačka","fa":"آلمان","de":"Deutschland","es":"Alemania","fr":"Allemagne","ja":"ドイツ","it":"Germania","ar":"ألمانيا"}',
                'latitude' => '51.00000000',
                'longitude' => '9.00000000',
            ),
            82 =>
            array(
                'name' => 'Ghana',
                'iso3' => 'GHA',
                'iso2' => 'GH',
                'dial_code' => '233',
                'currency' => 'GHS',
                'currency_symbol' => 'GH₵',
                'translations' => '{"br":"Gana","pt":"Gana","nl":"Ghana","hr":"Gana","fa":"غنا","de":"Ghana","es":"Ghana","fr":"Ghana","ja":"ガーナ","it":"Ghana","ar":"غانا"}',
                'latitude' => '8.00000000',
                'longitude' => '-2.00000000',
            ),
            83 =>
            array(
                'name' => 'Gibraltar',
                'iso3' => 'GIB',
                'iso2' => 'GI',
                'dial_code' => '350',
                'currency' => 'GIP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Gibraltar","pt":"Gibraltar","nl":"Gibraltar","hr":"Gibraltar","fa":"جبل‌طارق","de":"Gibraltar","es":"Gibraltar","fr":"Gibraltar","ja":"ジブラルタル","it":"Gibilterra","ar":"جبل طارق"}',
                'latitude' => '36.13333333',
                'longitude' => '-5.35000000',
            ),
            84 =>
            array(
                'name' => 'Greece',
                'iso3' => 'GRC',
                'iso2' => 'GR',
                'dial_code' => '30',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Grécia","pt":"Grécia","nl":"Griekenland","hr":"Grčka","fa":"یونان","de":"Griechenland","es":"Grecia","fr":"Grèce","ja":"ギリシャ","it":"Grecia","ar":"اليونان"}',
                'latitude' => '39.00000000',
                'longitude' => '22.00000000',
            ),
            85 =>
            array(
                'name' => 'Greenland',
                'iso3' => 'GRL',
                'iso2' => 'GL',
                'dial_code' => '299',
                'currency' => 'DKK',
                'currency_symbol' => 'Kr.',
                'translations' => '{"br":"Groelândia","pt":"Gronelândia","nl":"Groenland","hr":"Grenland","fa":"گرینلند","de":"Grönland","es":"Groenlandia","fr":"Groenland","ja":"グリーンランド","it":"Groenlandia","ar":"جرينلاند"}',
                'latitude' => '72.00000000',
                'longitude' => '-40.00000000',
            ),
            86 =>
            array(
                'name' => 'Grenada',
                'iso3' => 'GRD',
                'iso2' => 'GD',
                'dial_code' => '+1-473',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Granada","pt":"Granada","nl":"Grenada","hr":"Grenada","fa":"گرنادا","de":"Grenada","es":"Grenada","fr":"Grenade","ja":"グレナダ","it":"Grenada","ar":"غرينادا"}',
                'latitude' => '12.11666666',
                'longitude' => '-61.66666666',
            ),
            87 =>
            array(
                'name' => 'Guadeloupe',
                'iso3' => 'GLP',
                'iso2' => 'GP',
                'dial_code' => '590',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Guadalupe","pt":"Guadalupe","nl":"Guadeloupe","hr":"Gvadalupa","fa":"جزیره گوادلوپ","de":"Guadeloupe","es":"Guadalupe","fr":"Guadeloupe","ja":"グアドループ","it":"Guadeloupa","ar":"غوادلوب"}',
                'latitude' => '16.25000000',
                'longitude' => '-61.58333300',
            ),
            88 =>
            array(
                'name' => 'Guam',
                'iso3' => 'GUM',
                'iso2' => 'GU',
                'dial_code' => '+1-671',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Guam","pt":"Guame","nl":"Guam","hr":"Guam","fa":"گوام","de":"Guam","es":"Guam","fr":"Guam","ja":"グアム","it":"Guam","ar":"غوام"}',
                'latitude' => '13.46666666',
                'longitude' => '144.78333333',
            ),
            89 =>
            array(
                'name' => 'Guatemala',
                'iso3' => 'GTM',
                'iso2' => 'GT',
                'dial_code' => '502',
                'currency' => 'GTQ',
                'currency_symbol' => 'Q',
                'translations' => '{"br":"Guatemala","pt":"Guatemala","nl":"Guatemala","hr":"Gvatemala","fa":"گواتمالا","de":"Guatemala","es":"Guatemala","fr":"Guatemala","ja":"グアテマラ","it":"Guatemala","ar":"غواتيمالا"}',
                'latitude' => '15.50000000',
                'longitude' => '-90.25000000',
            ),
            90 =>
            array(
                'name' => 'Guernsey and Alderney',
                'iso3' => 'GGY',
                'iso2' => 'GG',
                'dial_code' => '+44-1481',
                'currency' => 'GBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Guernsey","pt":"Guernsey","nl":"Guernsey","hr":"Guernsey","fa":"گرنزی","de":"Guernsey","es":"Guernsey","fr":"Guernesey","ja":"ガーンジー","it":"Guernsey","ar":"غيرنزي وألديرني"}',
                'latitude' => '49.46666666',
                'longitude' => '-2.58333333',
            ),
            91 =>
            array(
                'name' => 'Guinea',
                'iso3' => 'GIN',
                'iso2' => 'GN',
                'dial_code' => '224',
                'currency' => 'GNF',
                'currency_symbol' => 'FG',
                'translations' => '{"br":"Guiné","pt":"Guiné","nl":"Guinee","hr":"Gvineja","fa":"گینه","de":"Guinea","es":"Guinea","fr":"Guinée","ja":"ギニア","it":"Guinea","ar":"غينيا"}',
                'latitude' => '11.00000000',
                'longitude' => '-10.00000000',
            ),
            92 =>
            array(
                'name' => 'Guinea-Bissau',
                'iso3' => 'GNB',
                'iso2' => 'GW',
                'dial_code' => '245',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Guiné-Bissau","pt":"Guiné-Bissau","nl":"Guinee-Bissau","hr":"Gvineja Bisau","fa":"گینه بیسائو","de":"Guinea-Bissau","es":"Guinea-Bisáu","fr":"Guinée-Bissau","ja":"ギニアビサウ","it":"Guinea-Bissau","ar":"غينيا بيساو"}',
                'latitude' => '12.00000000',
                'longitude' => '-15.00000000',
            ),
            93 =>
            array(
                'name' => 'Guyana',
                'iso3' => 'GUY',
                'iso2' => 'GY',
                'dial_code' => '592',
                'currency' => 'GYD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Guiana","pt":"Guiana","nl":"Guyana","hr":"Gvajana","fa":"گویان","de":"Guyana","es":"Guyana","fr":"Guyane","ja":"ガイアナ","it":"Guyana","ar":"غيانا"}',
                'latitude' => '5.00000000',
                'longitude' => '-59.00000000',
            ),
            94 =>
            array(
                'name' => 'Haiti',
                'iso3' => 'HTI',
                'iso2' => 'HT',
                'dial_code' => '509',
                'currency' => 'HTG',
                'currency_symbol' => 'G',
                'translations' => '{"br":"Haiti","pt":"Haiti","nl":"Haïti","hr":"Haiti","fa":"هائیتی","de":"Haiti","es":"Haiti","fr":"Haïti","ja":"ハイチ","it":"Haiti","ar":"هايتي"}',
                'latitude' => '19.00000000',
                'longitude' => '-72.41666666',
            ),
            95 =>
            array(
                'name' => 'Heard Island and McDonald Islands',
                'iso3' => 'HMD',
                'iso2' => 'HM',
                'dial_code' => '',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilha Heard e Ilhas McDonald","pt":"Ilha Heard e Ilhas McDonald","nl":"Heard- en McDonaldeilanden","hr":"Otok Heard i otočje McDonald","fa":"جزیره هرد و جزایر مک‌دونالد","de":"Heard und die McDonaldinseln","es":"Islas Heard y McDonald","fr":"Îles Heard-et-MacDonald","ja":"ハード島とマクドナルド諸島","it":"Isole Heard e McDonald","ar":"Heard Island and McDonald Islands"}',
                'latitude' => '-53.10000000',
                'longitude' => '72.51666666',
            ),
            96 =>
            array(
                'name' => 'Honduras',
                'iso3' => 'HND',
                'iso2' => 'HN',
                'dial_code' => '504',
                'currency' => 'HNL',
                'currency_symbol' => 'L',
                'translations' => '{"br":"Honduras","pt":"Honduras","nl":"Honduras","hr":"Honduras","fa":"هندوراس","de":"Honduras","es":"Honduras","fr":"Honduras","ja":"ホンジュラス","it":"Honduras","ar":"هندوراس"}',
                'latitude' => '15.00000000',
                'longitude' => '-86.50000000',
            ),
            97 =>
            array(
                'name' => 'Hong Kong S.A.R.',
                'iso3' => 'HKG',
                'iso2' => 'HK',
                'dial_code' => '852',
                'currency' => 'HKD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Hong Kong","pt":"Hong Kong","nl":"Hongkong","hr":"Hong Kong","fa":"هنگ‌کنگ","de":"Hong Kong","es":"Hong Kong","fr":"Hong Kong","ja":"香港","it":"Hong Kong","ar":"هونغ كونغ"}',
                'latitude' => '22.25000000',
                'longitude' => '114.16666666',
            ),
            98 =>
            array(
                'name' => 'Hungary',
                'iso3' => 'HUN',
                'iso2' => 'HU',
                'dial_code' => '36',
                'currency' => 'HUF',
                'currency_symbol' => 'Ft',
                'translations' => '{"br":"Hungria","pt":"Hungria","nl":"Hongarije","hr":"Mađarska","fa":"مجارستان","de":"Ungarn","es":"Hungría","fr":"Hongrie","ja":"ハンガリー","it":"Ungheria","ar":"المجر"}',
                'latitude' => '47.00000000',
                'longitude' => '20.00000000',
            ),
            99 =>
            array(
                'name' => 'Iceland',
                'iso3' => 'ISL',
                'iso2' => 'IS',
                'dial_code' => '354',
                'currency' => 'ISK',
                'currency_symbol' => 'kr',
                'translations' => '{"br":"Islândia","pt":"Islândia","nl":"IJsland","hr":"Island","fa":"ایسلند","de":"Island","es":"Islandia","fr":"Islande","ja":"アイスランド","it":"Islanda","ar":"آيسلندا"}',
                'latitude' => '65.00000000',
                'longitude' => '-18.00000000',
            ),
            100 =>
            array(
                'name' => 'India',
                'iso3' => 'IND',
                'iso2' => 'IN',
                'dial_code' => '91',
                'currency' => 'INR',
                'currency_symbol' => '₹',
                'translations' => '{"br":"Índia","pt":"Índia","nl":"India","hr":"Indija","fa":"هند","de":"Indien","es":"India","fr":"Inde","ja":"インド","it":"India","ar":"الهند"}',
                'latitude' => '20.00000000',
                'longitude' => '77.00000000',
            ),
            101 =>
            array(
                'name' => 'Indonesia',
                'iso3' => 'IDN',
                'iso2' => 'ID',
                'dial_code' => '62',
                'currency' => 'IDR',
                'currency_symbol' => 'Rp',
                'translations' => '{"br":"Indonésia","pt":"Indonésia","nl":"Indonesië","hr":"Indonezija","fa":"اندونزی","de":"Indonesien","es":"Indonesia","fr":"Indonésie","ja":"インドネシア","it":"Indonesia","ar":"إندونيسيا"}',
                'latitude' => '-5.00000000',
                'longitude' => '120.00000000',
            ),
            102 =>
            array(
                'name' => 'Iran',
                'iso3' => 'IRN',
                'iso2' => 'IR',
                'dial_code' => '98',
                'currency' => 'IRR',
                'currency_symbol' => '﷼',
                'translations' => '{"br":"Irã","pt":"Irão","nl":"Iran","hr":"Iran","fa":"ایران","de":"Iran","es":"Iran","fr":"Iran","ja":"イラン・イスラム共和国","ar":"إيران"}',
                'latitude' => '32.00000000',
                'longitude' => '53.00000000',
            ),
            103 =>
            array(
                'name' => 'Iraq',
                'iso3' => 'IRQ',
                'iso2' => 'IQ',
                'dial_code' => '964',
                'currency' => 'IQD',
                'currency_symbol' => 'د.ع',
                'translations' => '{"br":"Iraque","pt":"Iraque","nl":"Irak","hr":"Irak","fa":"عراق","de":"Irak","es":"Irak","fr":"Irak","ja":"イラク","it":"Iraq","ar":"العراق"}',
                'latitude' => '33.00000000',
                'longitude' => '44.00000000',
            ),
            104 =>
            array(
                'name' => 'Ireland',
                'iso3' => 'IRL',
                'iso2' => 'IE',
                'dial_code' => '353',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Irlanda","pt":"Irlanda","nl":"Ierland","hr":"Irska","fa":"ایرلند","de":"Irland","es":"Irlanda","fr":"Irlande","ja":"アイルランド","it":"Irlanda","ar":"أيرلندا"}',
                'latitude' => '53.00000000',
                'longitude' => '-8.00000000',
            ),
            105 =>
            array(
                'name' => 'Israel',
                'iso3' => 'ISR',
                'iso2' => 'IL',
                'dial_code' => '972',
                'currency' => 'ILS',
                'currency_symbol' => '₪',
                'translations' => '{"br":"Israel","pt":"Israel","nl":"Israël","hr":"Izrael","fa":"اسرائیل","de":"Israel","es":"Israel","fr":"Israël","ja":"イスラエル","it":"Israele","ar":"إسرائيل"}',
                'latitude' => '31.50000000',
                'longitude' => '34.75000000',
            ),
            106 =>
            array(
                'name' => 'Italy',
                'iso3' => 'ITA',
                'iso2' => 'IT',
                'dial_code' => '39',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Itália","pt":"Itália","nl":"Italië","hr":"Italija","fa":"ایتالیا","de":"Italien","es":"Italia","fr":"Italie","ja":"イタリア","it":"Italia","ar":"إيطاليا"}',
                'latitude' => '42.83333333',
                'longitude' => '12.83333333',
            ),
            107 =>
            array(
                'name' => 'Jamaica',
                'iso3' => 'JAM',
                'iso2' => 'JM',
                'dial_code' => '+1-876',
                'currency' => 'JMD',
                'currency_symbol' => 'J$',
                'translations' => '{"br":"Jamaica","pt":"Jamaica","nl":"Jamaica","hr":"Jamajka","fa":"جامائیکا","de":"Jamaika","es":"Jamaica","fr":"Jamaïque","ja":"ジャマイカ","it":"Giamaica","ar":"جامايكا"}',
                'latitude' => '18.25000000',
                'longitude' => '-77.50000000',
            ),
            108 =>
            array(
                'name' => 'Japan',
                'iso3' => 'JPN',
                'iso2' => 'JP',
                'dial_code' => '81',
                'currency' => 'JPY',
                'currency_symbol' => '¥',
                'translations' => '{"br":"Japão","pt":"Japão","nl":"Japan","hr":"Japan","fa":"ژاپن","de":"Japan","es":"Japón","fr":"Japon","ja":"日本","it":"Giappone","ar":"اليابان"}',
                'latitude' => '36.00000000',
                'longitude' => '138.00000000',
            ),
            109 =>
            array(
                'name' => 'Jersey',
                'iso3' => 'JEY',
                'iso2' => 'JE',
                'dial_code' => '+44-1534',
                'currency' => 'GBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Jersey","pt":"Jersey","nl":"Jersey","hr":"Jersey","fa":"جرزی","de":"Jersey","es":"Jersey","fr":"Jersey","ja":"ジャージー","it":"Isola di Jersey","ar":"جيرسي"}',
                'latitude' => '49.25000000',
                'longitude' => '-2.16666666',
            ),
            110 =>
            array(
                'name' => 'Jordan',
                'iso3' => 'JOR',
                'iso2' => 'JO',
                'dial_code' => '962',
                'currency' => 'JOD',
                'currency_symbol' => 'ا.د',
                'translations' => '{"br":"Jordânia","pt":"Jordânia","nl":"Jordanië","hr":"Jordan","fa":"اردن","de":"Jordanien","es":"Jordania","fr":"Jordanie","ja":"ヨルダン","it":"Giordania","ar":"الأردن"}',
                'latitude' => '31.00000000',
                'longitude' => '36.00000000',
            ),
            111 =>
            array(
                'name' => 'Kazakhstan',
                'iso3' => 'KAZ',
                'iso2' => 'KZ',
                'dial_code' => '7',
                'currency' => 'KZT',
                'currency_symbol' => 'лв',
                'translations' => '{"br":"Cazaquistão","pt":"Cazaquistão","nl":"Kazachstan","hr":"Kazahstan","fa":"قزاقستان","de":"Kasachstan","es":"Kazajistán","fr":"Kazakhstan","ja":"カザフスタン","it":"Kazakistan","ar":"كازاخستان"}',
                'latitude' => '48.00000000',
                'longitude' => '68.00000000',
            ),
            112 =>
            array(
                'name' => 'Kenya',
                'iso3' => 'KEN',
                'iso2' => 'KE',
                'dial_code' => '254',
                'currency' => 'KES',
                'currency_symbol' => 'KSh',
                'translations' => '{"br":"Quênia","pt":"Quénia","nl":"Kenia","hr":"Kenija","fa":"کنیا","de":"Kenia","es":"Kenia","fr":"Kenya","ja":"ケニア","it":"Kenya","ar":"كينيا"}',
                'latitude' => '1.00000000',
                'longitude' => '38.00000000',
            ),
            113 =>
            array(
                'name' => 'Kiribati',
                'iso3' => 'KIR',
                'iso2' => 'KI',
                'dial_code' => '686',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Kiribati","pt":"Quiribáti","nl":"Kiribati","hr":"Kiribati","fa":"کیریباتی","de":"Kiribati","es":"Kiribati","fr":"Kiribati","ja":"キリバス","it":"Kiribati","ar":"كيريباتي"}',
                'latitude' => '1.41666666',
                'longitude' => '173.00000000',
            ),
            114 =>
            array(
                'name' => 'Korea North',
                'iso3' => 'PRK',
                'iso2' => 'KP',
                'dial_code' => '850',
                'currency' => 'KPW',
                'currency_symbol' => '₩',
                'translations' => '{"br":"Coreia do Norte","pt":"Coreia do Norte","nl":"Noord-Korea","hr":"Sjeverna Koreja","fa":"کره جنوبی","de":"Nordkorea","es":"Corea del Norte","fr":"Corée du Nord","ja":"朝鮮民主主義人民共和国","it":"Corea del Nord","ar":"كوريا الشمالية"}',
                'latitude' => '40.00000000',
                'longitude' => '127.00000000',
            ),
            115 =>
            array(
                'name' => 'Korea South',
                'iso3' => 'KOR',
                'iso2' => 'KR',
                'dial_code' => '82',
                'currency' => 'KRW',
                'currency_symbol' => '₩',
                'translations' => '{"br":"Coreia do Sul","pt":"Coreia do Sul","nl":"Zuid-Korea","hr":"Južna Koreja","fa":"کره شمالی","de":"Südkorea","es":"Corea del Sur","fr":"Corée du Sud","ja":"大韓民国","it":"Corea del Sud","ar":"كوريا الجنوبية"}',
                'latitude' => '37.00000000',
                'longitude' => '127.50000000',
            ),
            116 =>
            array(
                'name' => 'Kuwait',
                'iso3' => 'KWT',
                'iso2' => 'KW',
                'dial_code' => '965',
                'currency' => 'KWD',
                'currency_symbol' => 'ك.د',
                'translations' => '{"br":"Kuwait","pt":"Kuwait","nl":"Koeweit","hr":"Kuvajt","fa":"کویت","de":"Kuwait","es":"Kuwait","fr":"Koweït","ja":"クウェート","it":"Kuwait","ar":"الكويت"}',
                'latitude' => '29.50000000',
                'longitude' => '45.75000000',
            ),
            117 =>
            array(
                'name' => 'Kyrgyzstan',
                'iso3' => 'KGZ',
                'iso2' => 'KG',
                'dial_code' => '996',
                'currency' => 'KGS',
                'currency_symbol' => 'лв',
                'translations' => '{"br":"Quirguistão","pt":"Quirguizistão","nl":"Kirgizië","hr":"Kirgistan","fa":"قرقیزستان","de":"Kirgisistan","es":"Kirguizistán","fr":"Kirghizistan","ja":"キルギス","it":"Kirghizistan","ar":"قيرغيزستان"}',
                'latitude' => '41.00000000',
                'longitude' => '75.00000000',
            ),
            118 =>
            array(
                'name' => 'Laos',
                'iso3' => 'LAO',
                'iso2' => 'LA',
                'dial_code' => '856',
                'currency' => 'LAK',
                'currency_symbol' => '₭',
                'translations' => '{"br":"Laos","pt":"Laos","nl":"Laos","hr":"Laos","fa":"لائوس","de":"Laos","es":"Laos","fr":"Laos","ja":"ラオス人民民主共和国","it":"Laos","ar":"لاوس"}',
                'latitude' => '18.00000000',
                'longitude' => '105.00000000',
            ),
            119 =>
            array(
                'name' => 'Latvia',
                'iso3' => 'LVA',
                'iso2' => 'LV',
                'dial_code' => '371',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Letônia","pt":"Letónia","nl":"Letland","hr":"Latvija","fa":"لتونی","de":"Lettland","es":"Letonia","fr":"Lettonie","ja":"ラトビア","it":"Lettonia","ar":"لاتفيا"}',
                'latitude' => '57.00000000',
                'longitude' => '25.00000000',
            ),
            120 =>
            array(
                'name' => 'Lebanon',
                'iso3' => 'LBN',
                'iso2' => 'LB',
                'dial_code' => '961',
                'currency' => 'LBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Líbano","pt":"Líbano","nl":"Libanon","hr":"Libanon","fa":"لبنان","de":"Libanon","es":"Líbano","fr":"Liban","ja":"レバノン","it":"Libano","ar":"لبنان"}',
                'latitude' => '33.83333333',
                'longitude' => '35.83333333',
            ),
            121 =>
            array(
                'name' => 'Lesotho',
                'iso3' => 'LSO',
                'iso2' => 'LS',
                'dial_code' => '266',
                'currency' => 'LSL',
                'currency_symbol' => 'L',
                'translations' => '{"br":"Lesoto","pt":"Lesoto","nl":"Lesotho","hr":"Lesoto","fa":"لسوتو","de":"Lesotho","es":"Lesotho","fr":"Lesotho","ja":"レソト","it":"Lesotho","ar":"ليسوتو"}',
                'latitude' => '-29.50000000',
                'longitude' => '28.50000000',
            ),
            122 =>
            array(
                'name' => 'Liberia',
                'iso3' => 'LBR',
                'iso2' => 'LR',
                'dial_code' => '231',
                'currency' => 'LRD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Libéria","pt":"Libéria","nl":"Liberia","hr":"Liberija","fa":"لیبریا","de":"Liberia","es":"Liberia","fr":"Liberia","ja":"リベリア","it":"Liberia","ar":"ليبيريا"}',
                'latitude' => '6.50000000',
                'longitude' => '-9.50000000',
            ),
            123 =>
            array(
                'name' => 'Libya',
                'iso3' => 'LBY',
                'iso2' => 'LY',
                'dial_code' => '218',
                'currency' => 'LYD',
                'currency_symbol' => 'د.ل',
                'translations' => '{"br":"Líbia","pt":"Líbia","nl":"Libië","hr":"Libija","fa":"لیبی","de":"Libyen","es":"Libia","fr":"Libye","ja":"リビア","it":"Libia","ar":"ليبيا"}',
                'latitude' => '25.00000000',
                'longitude' => '17.00000000',
            ),
            124 =>
            array(
                'name' => 'Liechtenstein',
                'iso3' => 'LIE',
                'iso2' => 'LI',
                'dial_code' => '423',
                'currency' => 'CHF',
                'currency_symbol' => 'CHf',
                'translations' => '{"br":"Liechtenstein","pt":"Listenstaine","nl":"Liechtenstein","hr":"Lihtenštajn","fa":"لیختن‌اشتاین","de":"Liechtenstein","es":"Liechtenstein","fr":"Liechtenstein","ja":"リヒテンシュタイン","it":"Liechtenstein","ar":"ليختنشتاين"}',
                'latitude' => '47.26666666',
                'longitude' => '9.53333333',
            ),
            125 =>
            array(
                'name' => 'Lithuania',
                'iso3' => 'LTU',
                'iso2' => 'LT',
                'dial_code' => '370',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Lituânia","pt":"Lituânia","nl":"Litouwen","hr":"Litva","fa":"لیتوانی","de":"Litauen","es":"Lituania","fr":"Lituanie","ja":"リトアニア","it":"Lituania","ar":"ليتوانيا"}',
                'latitude' => '56.00000000',
                'longitude' => '24.00000000',
            ),
            126 =>
            array(
                'name' => 'Luxembourg',
                'iso3' => 'LUX',
                'iso2' => 'LU',
                'dial_code' => '352',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Luxemburgo","pt":"Luxemburgo","nl":"Luxemburg","hr":"Luksemburg","fa":"لوکزامبورگ","de":"Luxemburg","es":"Luxemburgo","fr":"Luxembourg","ja":"ルクセンブルク","it":"Lussemburgo","ar":"لوكسمبورغ"}',
                'latitude' => '49.75000000',
                'longitude' => '6.16666666',
            ),
            127 =>
            array(
                'name' => 'Macau S.A.R.',
                'iso3' => 'MAC',
                'iso2' => 'MO',
                'dial_code' => '853',
                'currency' => 'MOP',
                'currency_symbol' => '$',
                'translations' => '{"br":"Macau","pt":"Macau","nl":"Macao","hr":"Makao","fa":"مکائو","de":"Macao","es":"Macao","fr":"Macao","ja":"マカオ","it":"Macao","ar":"ماكاو"}',
                'latitude' => '22.16666666',
                'longitude' => '113.55000000',
            ),
            128 =>
            array(
                'name' => 'Macedonia',
                'iso3' => 'MKD',
                'iso2' => 'MK',
                'dial_code' => '389',
                'currency' => 'MKD',
                'currency_symbol' => 'ден',
                'translations' => '{"br":"Macedônia","pt":"Macedónia","nl":"Macedonië","hr":"Makedonija","fa":"","de":"Mazedonien","es":"Macedonia","fr":"Macédoine","ja":"マケドニア旧ユーゴスラビア共和国","it":"Macedonia","ar":"مقدونيا"}',
                'latitude' => '41.83333333',
                'longitude' => '22.00000000',
            ),
            129 =>
            array(
                'name' => 'Madagascar',
                'iso3' => 'MDG',
                'iso2' => 'MG',
                'dial_code' => '261',
                'currency' => 'MGA',
                'currency_symbol' => 'Ar',
                'translations' => '{"br":"Madagascar","pt":"Madagáscar","nl":"Madagaskar","hr":"Madagaskar","fa":"ماداگاسکار","de":"Madagaskar","es":"Madagascar","fr":"Madagascar","ja":"マダガスカル","it":"Madagascar","ar":"مدغشقر"}',
                'latitude' => '-20.00000000',
                'longitude' => '47.00000000',
            ),
            130 =>
            array(
                'name' => 'Malawi',
                'iso3' => 'MWI',
                'iso2' => 'MW',
                'dial_code' => '265',
                'currency' => 'MWK',
                'currency_symbol' => 'MK',
                'translations' => '{"br":"Malawi","pt":"Malávi","nl":"Malawi","hr":"Malavi","fa":"مالاوی","de":"Malawi","es":"Malawi","fr":"Malawi","ja":"マラウイ","it":"Malawi","ar":"ملاوي"}',
                'latitude' => '-13.50000000',
                'longitude' => '34.00000000',
            ),
            131 =>
            array(
                'name' => 'Malaysia',
                'iso3' => 'MYS',
                'iso2' => 'MY',
                'dial_code' => '60',
                'currency' => 'MYR',
                'currency_symbol' => 'RM',
                'translations' => '{"br":"Malásia","pt":"Malásia","nl":"Maleisië","hr":"Malezija","fa":"مالزی","de":"Malaysia","es":"Malasia","fr":"Malaisie","ja":"マレーシア","it":"Malesia","ar":"ماليزيا"}',
                'latitude' => '2.50000000',
                'longitude' => '112.50000000',
            ),
            132 =>
            array(
                'name' => 'Maldives',
                'iso3' => 'MDV',
                'iso2' => 'MV',
                'dial_code' => '960',
                'currency' => 'MVR',
                'currency_symbol' => 'Rf',
                'translations' => '{"br":"Maldivas","pt":"Maldivas","nl":"Maldiven","hr":"Maldivi","fa":"مالدیو","de":"Malediven","es":"Maldivas","fr":"Maldives","ja":"モルディブ","it":"Maldive","ar":"جزر المالديف"}',
                'latitude' => '3.25000000',
                'longitude' => '73.00000000',
            ),
            133 =>
            array(
                'name' => 'Mali',
                'iso3' => 'MLI',
                'iso2' => 'ML',
                'dial_code' => '223',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Mali","pt":"Mali","nl":"Mali","hr":"Mali","fa":"مالی","de":"Mali","es":"Mali","fr":"Mali","ja":"マリ","it":"Mali","ar":"مالي"}',
                'latitude' => '17.00000000',
                'longitude' => '-4.00000000',
            ),
            134 =>
            array(
                'name' => 'Malta',
                'iso3' => 'MLT',
                'iso2' => 'MT',
                'dial_code' => '356',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Malta","pt":"Malta","nl":"Malta","hr":"Malta","fa":"مالت","de":"Malta","es":"Malta","fr":"Malte","ja":"マルタ","it":"Malta","ar":"مالطا"}',
                'latitude' => '35.83333333',
                'longitude' => '14.58333333',
            ),
            135 =>
            array(
                'name' => 'Man (Isle of)',
                'iso3' => 'IMN',
                'iso2' => 'IM',
                'dial_code' => '+44-1624',
                'currency' => 'GBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Ilha de Man","pt":"Ilha de Man","nl":"Isle of Man","hr":"Otok Man","fa":"جزیره من","de":"Insel Man","es":"Isla de Man","fr":"Île de Man","ja":"マン島","it":"Isola di Man","ar":"جزيرة مان"}',
                'latitude' => '54.25000000',
                'longitude' => '-4.50000000',
            ),
            136 =>
            array(
                'name' => 'Marshall Islands',
                'iso3' => 'MHL',
                'iso2' => 'MH',
                'dial_code' => '692',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Marshall","pt":"Ilhas Marshall","nl":"Marshalleilanden","hr":"Maršalovi Otoci","fa":"جزایر مارشال","de":"Marshallinseln","es":"Islas Marshall","fr":"Îles Marshall","ja":"マーシャル諸島","it":"Isole Marshall","ar":"جزر مارشال"}',
                'latitude' => '9.00000000',
                'longitude' => '168.00000000',
            ),
            137 =>
            array(
                'name' => 'Martinique',
                'iso3' => 'MTQ',
                'iso2' => 'MQ',
                'dial_code' => '596',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Martinica","pt":"Martinica","nl":"Martinique","hr":"Martinique","fa":"مونتسرات","de":"Martinique","es":"Martinica","fr":"Martinique","ja":"マルティニーク","it":"Martinica","ar":"مارتينيك"}',
                'latitude' => '14.66666700',
                'longitude' => '-61.00000000',
            ),
            138 =>
            array(
                'name' => 'Mauritania',
                'iso3' => 'MRT',
                'iso2' => 'MR',
                'dial_code' => '222',
                'currency' => 'MRO',
                'currency_symbol' => 'MRU',
                'translations' => '{"br":"Mauritânia","pt":"Mauritânia","nl":"Mauritanië","hr":"Mauritanija","fa":"موریتانی","de":"Mauretanien","es":"Mauritania","fr":"Mauritanie","ja":"モーリタニア","it":"Mauritania","ar":"موريتانيا"}',
                'latitude' => '20.00000000',
                'longitude' => '-12.00000000',
            ),
            139 =>
            array(
                'name' => 'Mauritius',
                'iso3' => 'MUS',
                'iso2' => 'MU',
                'dial_code' => '230',
                'currency' => 'MUR',
                'currency_symbol' => '₨',
                'translations' => '{"br":"Maurício","pt":"Maurícia","nl":"Mauritius","hr":"Mauricijus","fa":"موریس","de":"Mauritius","es":"Mauricio","fr":"Île Maurice","ja":"モーリシャス","it":"Mauritius","ar":"موريشيوس"}',
                'latitude' => '-20.28333333',
                'longitude' => '57.55000000',
            ),
            140 =>
            array(
                'name' => 'Mayotte',
                'iso3' => 'MYT',
                'iso2' => 'YT',
                'dial_code' => '262',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Mayotte","pt":"Mayotte","nl":"Mayotte","hr":"Mayotte","fa":"مایوت","de":"Mayotte","es":"Mayotte","fr":"Mayotte","ja":"マヨット","it":"Mayotte","ar":"مايوت"}',
                'latitude' => '-12.83333333',
                'longitude' => '45.16666666',
            ),
            141 =>
            array(
                'name' => 'Mexico',
                'iso3' => 'MEX',
                'iso2' => 'MX',
                'dial_code' => '52',
                'currency' => 'MXN',
                'currency_symbol' => '$',
                'translations' => '{"br":"México","pt":"México","nl":"Mexico","hr":"Meksiko","fa":"مکزیک","de":"Mexiko","es":"México","fr":"Mexique","ja":"メキシコ","it":"Messico","ar":"المكسيك"}',
                'latitude' => '23.00000000',
                'longitude' => '-102.00000000',
            ),
            142 =>
            array(
                'name' => 'Micronesia',
                'iso3' => 'FSM',
                'iso2' => 'FM',
                'dial_code' => '691',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Micronésia","pt":"Micronésia","nl":"Micronesië","hr":"Mikronezija","fa":"ایالات فدرال میکرونزی","de":"Mikronesien","es":"Micronesia","fr":"Micronésie","ja":"ミクロネシア連邦","it":"Micronesia","ar":"ميكرونيزيا"}',
                'latitude' => '6.91666666',
                'longitude' => '158.25000000',
            ),
            143 =>
            array(
                'name' => 'Moldova',
                'iso3' => 'MDA',
                'iso2' => 'MD',
                'dial_code' => '373',
                'currency' => 'MDL',
                'currency_symbol' => 'L',
                'translations' => '{"br":"Moldávia","pt":"Moldávia","nl":"Moldavië","hr":"Moldova","fa":"مولداوی","de":"Moldawie","es":"Moldavia","fr":"Moldavie","ja":"モルドバ共和国","it":"Moldavia","ar":"مولدوفا"}',
                'latitude' => '47.00000000',
                'longitude' => '29.00000000',
            ),
            144 =>
            array(
                'name' => 'Monaco',
                'iso3' => 'MCO',
                'iso2' => 'MC',
                'dial_code' => '377',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Mônaco","pt":"Mónaco","nl":"Monaco","hr":"Monako","fa":"موناکو","de":"Monaco","es":"Mónaco","fr":"Monaco","ja":"モナコ","it":"Principato di Monaco","ar":"موناكو"}',
                'latitude' => '43.73333333',
                'longitude' => '7.40000000',
            ),
            145 =>
            array(
                'name' => 'Mongolia',
                'iso3' => 'MNG',
                'iso2' => 'MN',
                'dial_code' => '976',
                'currency' => 'MNT',
                'currency_symbol' => '₮',
                'translations' => '{"br":"Mongólia","pt":"Mongólia","nl":"Mongolië","hr":"Mongolija","fa":"مغولستان","de":"Mongolei","es":"Mongolia","fr":"Mongolie","ja":"モンゴル","it":"Mongolia","ar":"منغوليا"}',
                'latitude' => '46.00000000',
                'longitude' => '105.00000000',
            ),
            146 =>
            array(
                'name' => 'Montenegro',
                'iso3' => 'MNE',
                'iso2' => 'ME',
                'dial_code' => '382',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Montenegro","pt":"Montenegro","nl":"Montenegro","hr":"Crna Gora","fa":"مونته‌نگرو","de":"Montenegro","es":"Montenegro","fr":"Monténégro","ja":"モンテネグロ","it":"Montenegro","ar":"الجبل الأسود"}',
                'latitude' => '42.50000000',
                'longitude' => '19.30000000',
            ),
            147 =>
            array(
                'name' => 'Montserrat',
                'iso3' => 'MSR',
                'iso2' => 'MS',
                'dial_code' => '+1-664',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Montserrat","pt":"Monserrate","nl":"Montserrat","hr":"Montserrat","fa":"مایوت","de":"Montserrat","es":"Montserrat","fr":"Montserrat","ja":"モントセラト","it":"Montserrat","ar":"مونتسرات"}',
                'latitude' => '16.75000000',
                'longitude' => '-62.20000000',
            ),
            148 =>
            array(
                'name' => 'Morocco',
                'iso3' => 'MAR',
                'iso2' => 'MA',
                'dial_code' => '212',
                'currency' => 'MAD',
                'currency_symbol' => 'DH',
                'translations' => '{"br":"Marrocos","pt":"Marrocos","nl":"Marokko","hr":"Maroko","fa":"مراکش","de":"Marokko","es":"Marruecos","fr":"Maroc","ja":"モロッコ","it":"Marocco","ar":"المغرب"}',
                'latitude' => '32.00000000',
                'longitude' => '-5.00000000',
            ),
            149 =>
            array(
                'name' => 'Mozambique',
                'iso3' => 'MOZ',
                'iso2' => 'MZ',
                'dial_code' => '258',
                'currency' => 'MZN',
                'currency_symbol' => 'MT',
                'translations' => '{"br":"Moçambique","pt":"Moçambique","nl":"Mozambique","hr":"Mozambik","fa":"موزامبیک","de":"Mosambik","es":"Mozambique","fr":"Mozambique","ja":"モザンビーク","it":"Mozambico","ar":"موزمبيق"}',
                'latitude' => '-18.25000000',
                'longitude' => '35.00000000',
            ),
            150 =>
            array(
                'name' => 'Myanmar',
                'iso3' => 'MMR',
                'iso2' => 'MM',
                'dial_code' => '95',
                'currency' => 'MMK',
                'currency_symbol' => 'K',
                'translations' => '{"br":"Myanmar","pt":"Myanmar","nl":"Myanmar","hr":"Mijanmar","fa":"میانمار","de":"Myanmar","es":"Myanmar","fr":"Myanmar","ja":"ミャンマー","it":"Birmania","ar":"ميانمار"}',
                'latitude' => '22.00000000',
                'longitude' => '98.00000000',
            ),
            151 =>
            array(
                'name' => 'Namibia',
                'iso3' => 'NAM',
                'iso2' => 'NA',
                'dial_code' => '264',
                'currency' => 'NAD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Namíbia","pt":"Namíbia","nl":"Namibië","hr":"Namibija","fa":"نامیبیا","de":"Namibia","es":"Namibia","fr":"Namibie","ja":"ナミビア","it":"Namibia","ar":"ناميبيا"}',
                'latitude' => '-22.00000000',
                'longitude' => '17.00000000',
            ),
            152 =>
            array(
                'name' => 'Nauru',
                'iso3' => 'NRU',
                'iso2' => 'NR',
                'dial_code' => '674',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Nauru","pt":"Nauru","nl":"Nauru","hr":"Nauru","fa":"نائورو","de":"Nauru","es":"Nauru","fr":"Nauru","ja":"ナウル","it":"Nauru","ar":"ناورو"}',
                'latitude' => '-0.53333333',
                'longitude' => '166.91666666',
            ),
            153 =>
            array(
                'name' => 'Nepal',
                'iso3' => 'NPL',
                'iso2' => 'NP',
                'dial_code' => '977',
                'currency' => 'NPR',
                'currency_symbol' => '₨',
                'translations' => '{"br":"Nepal","pt":"Nepal","nl":"Nepal","hr":"Nepal","fa":"نپال","de":"Népal","es":"Nepal","fr":"Népal","ja":"ネパール","it":"Nepal","ar":"نيبال"}',
                'latitude' => '28.00000000',
                'longitude' => '84.00000000',
            ),
            154 =>
            array(
                'name' => 'Bonaire, Sint Eustatius and Saba',
                'iso3' => 'BES',
                'iso2' => 'BQ',
                'dial_code' => '599',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Bonaire","pt":"Bonaire","fa":"بونیر","de":"Bonaire, Sint Eustatius und Saba","fr":"Bonaire, Saint-Eustache et Saba","it":"Bonaire, Saint-Eustache e Saba","ar":"Bonaire, Sint Eustatius and Saba"}',
                'latitude' => '12.15000000',
                'longitude' => '-68.26666700',
            ),
            155 =>
            array(
                'name' => 'Netherlands The',
                'iso3' => 'NLD',
                'iso2' => 'NL',
                'dial_code' => '31',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Holanda","pt":"Países Baixos","nl":"Nederland","hr":"Nizozemska","fa":"پادشاهی هلند","de":"Niederlande","es":"Países Bajos","fr":"Pays-Bas","ja":"オランダ","it":"Paesi Bassi","ar":"Netherlands The"}',
                'latitude' => '52.50000000',
                'longitude' => '5.75000000',
            ),
            156 =>
            array(
                'name' => 'New Caledonia',
                'iso3' => 'NCL',
                'iso2' => 'NC',
                'dial_code' => '687',
                'currency' => 'XPF',
                'currency_symbol' => '₣',
                'translations' => '{"br":"Nova Caledônia","pt":"Nova Caledónia","nl":"Nieuw-Caledonië","hr":"Nova Kaledonija","fa":"کالدونیای جدید","de":"Neukaledonien","es":"Nueva Caledonia","fr":"Nouvelle-Calédonie","ja":"ニューカレドニア","it":"Nuova Caledonia","ar":"كاليدونيا الجديدة"}',
                'latitude' => '-21.50000000',
                'longitude' => '165.50000000',
            ),
            157 =>
            array(
                'name' => 'New Zealand',
                'iso3' => 'NZL',
                'iso2' => 'NZ',
                'dial_code' => '64',
                'currency' => 'NZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Nova Zelândia","pt":"Nova Zelândia","nl":"Nieuw-Zeeland","hr":"Novi Zeland","fa":"نیوزیلند","de":"Neuseeland","es":"Nueva Zelanda","fr":"Nouvelle-Zélande","ja":"ニュージーランド","it":"Nuova Zelanda","ar":"نيوزيلندا"}',
                'latitude' => '-41.00000000',
                'longitude' => '174.00000000',
            ),
            158 =>
            array(
                'name' => 'Nicaragua',
                'iso3' => 'NIC',
                'iso2' => 'NI',
                'dial_code' => '505',
                'currency' => 'NIO',
                'currency_symbol' => 'C$',
                'translations' => '{"br":"Nicarágua","pt":"Nicarágua","nl":"Nicaragua","hr":"Nikaragva","fa":"نیکاراگوئه","de":"Nicaragua","es":"Nicaragua","fr":"Nicaragua","ja":"ニカラグア","it":"Nicaragua","ar":"نيكاراغوا"}',
                'latitude' => '13.00000000',
                'longitude' => '-85.00000000',
            ),
            159 =>
            array(
                'name' => 'Niger',
                'iso3' => 'NER',
                'iso2' => 'NE',
                'dial_code' => '227',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Níger","pt":"Níger","nl":"Niger","hr":"Niger","fa":"نیجر","de":"Niger","es":"Níger","fr":"Niger","ja":"ニジェール","it":"Niger","ar":"النيجر"}',
                'latitude' => '16.00000000',
                'longitude' => '8.00000000',
            ),
            160 =>
            array(
                'name' => 'Nigeria',
                'iso3' => 'NGA',
                'iso2' => 'NG',
                'dial_code' => '234',
                'currency' => 'NGN',
                'currency_symbol' => '₦',
                'translations' => '{"br":"Nigéria","pt":"Nigéria","nl":"Nigeria","hr":"Nigerija","fa":"نیجریه","de":"Nigeria","es":"Nigeria","fr":"Nigéria","ja":"ナイジェリア","it":"Nigeria","ar":"نيجيريا"}',
                'latitude' => '10.00000000',
                'longitude' => '8.00000000',
            ),
            161 =>
            array(
                'name' => 'Niue',
                'iso3' => 'NIU',
                'iso2' => 'NU',
                'dial_code' => '683',
                'currency' => 'NZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Niue","pt":"Niue","nl":"Niue","hr":"Niue","fa":"نیووی","de":"Niue","es":"Niue","fr":"Niue","ja":"ニウエ","it":"Niue","ar":"نيوي"}',
                'latitude' => '-19.03333333',
                'longitude' => '-169.86666666',
            ),
            162 =>
            array(
                'name' => 'Norfolk Island',
                'iso3' => 'NFK',
                'iso2' => 'NF',
                'dial_code' => '672',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilha Norfolk","pt":"Ilha Norfolk","nl":"Norfolkeiland","hr":"Otok Norfolk","fa":"جزیره نورفک","de":"Norfolkinsel","es":"Isla de Norfolk","fr":"Île de Norfolk","ja":"ノーフォーク島","it":"Isola Norfolk","ar":"جزيرة نورفولك"}',
                'latitude' => '-29.03333333',
                'longitude' => '167.95000000',
            ),
            163 =>
            array(
                'name' => 'Northern Mariana Islands',
                'iso3' => 'MNP',
                'iso2' => 'MP',
                'dial_code' => '+1-670',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Marianas","pt":"Ilhas Marianas","nl":"Noordelijke Marianeneilanden","hr":"Sjevernomarijanski otoci","fa":"جزایر ماریانای شمالی","de":"Nördliche Marianen","es":"Islas Marianas del Norte","fr":"Îles Mariannes du Nord","ja":"北マリアナ諸島","it":"Isole Marianne Settentrionali","ar":"جزر ماريانا الشمالية"}',
                'latitude' => '15.20000000',
                'longitude' => '145.75000000',
            ),
            164 =>
            array(
                'name' => 'Norway',
                'iso3' => 'NOR',
                'iso2' => 'NO',
                'dial_code' => '47',
                'currency' => 'NOK',
                'currency_symbol' => 'kr',
                'translations' => '{"br":"Noruega","pt":"Noruega","nl":"Noorwegen","hr":"Norveška","fa":"نروژ","de":"Norwegen","es":"Noruega","fr":"Norvège","ja":"ノルウェー","it":"Norvegia","ar":"النرويج"}',
                'latitude' => '62.00000000',
                'longitude' => '10.00000000',
            ),
            165 =>
            array(
                'name' => 'Oman',
                'iso3' => 'OMN',
                'iso2' => 'OM',
                'dial_code' => '968',
                'currency' => 'OMR',
                'currency_symbol' => '.ع.ر',
                'translations' => '{"br":"Omã","pt":"Omã","nl":"Oman","hr":"Oman","fa":"عمان","de":"Oman","es":"Omán","fr":"Oman","ja":"オマーン","it":"oman","ar":"عمان"}',
                'latitude' => '21.00000000',
                'longitude' => '57.00000000',
            ),
            166 =>
            array(
                'name' => 'Pakistan',
                'iso3' => 'PAK',
                'iso2' => 'PK',
                'dial_code' => '92',
                'currency' => 'PKR',
                'currency_symbol' => '₨',
                'translations' => '{"br":"Paquistão","pt":"Paquistão","nl":"Pakistan","hr":"Pakistan","fa":"پاکستان","de":"Pakistan","es":"Pakistán","fr":"Pakistan","ja":"パキスタン","it":"Pakistan","ar":"باكستان"}',
                'latitude' => '30.00000000',
                'longitude' => '70.00000000',
            ),
            167 =>
            array(
                'name' => 'Palau',
                'iso3' => 'PLW',
                'iso2' => 'PW',
                'dial_code' => '680',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Palau","pt":"Palau","nl":"Palau","hr":"Palau","fa":"پالائو","de":"Palau","es":"Palau","fr":"Palaos","ja":"パラオ","it":"Palau","ar":"بالاو"}',
                'latitude' => '7.50000000',
                'longitude' => '134.50000000',
            ),
            168 =>
            array(
                'name' => 'Palestinian Territory Occupied',
                'iso3' => 'PSE',
                'iso2' => 'PS',
                'dial_code' => '970',
                'currency' => 'ILS',
                'currency_symbol' => '₪',
                'translations' => '{"br":"Palestina","pt":"Palestina","nl":"Palestijnse gebieden","hr":"Palestina","fa":"فلسطین","de":"Palästina","es":"Palestina","fr":"Palestine","ja":"パレスチナ","it":"Palestina","ar":"الأراضي الفلسطينية المحتلة"}',
                'latitude' => '31.90000000',
                'longitude' => '35.20000000',
            ),
            169 =>
            array(
                'name' => 'Panama',
                'iso3' => 'PAN',
                'iso2' => 'PA',
                'dial_code' => '507',
                'currency' => 'PAB',
                'currency_symbol' => 'B/.',
                'translations' => '{"br":"Panamá","pt":"Panamá","nl":"Panama","hr":"Panama","fa":"پاناما","de":"Panama","es":"Panamá","fr":"Panama","ja":"パナマ","it":"Panama","ar":"بنما"}',
                'latitude' => '9.00000000',
                'longitude' => '-80.00000000',
            ),
            170 =>
            array(
                'name' => 'Papua new Guinea',
                'iso3' => 'PNG',
                'iso2' => 'PG',
                'dial_code' => '675',
                'currency' => 'PGK',
                'currency_symbol' => 'K',
                'translations' => '{"br":"Papua Nova Guiné","pt":"Papua Nova Guiné","nl":"Papoea-Nieuw-Guinea","hr":"Papua Nova Gvineja","fa":"پاپوآ گینه نو","de":"Papua-Neuguinea","es":"Papúa Nueva Guinea","fr":"Papouasie-Nouvelle-Guinée","ja":"パプアニューギニア","it":"Papua Nuova Guinea","ar":"بابوا غينيا الجديدة"}',
                'latitude' => '-6.00000000',
                'longitude' => '147.00000000',
            ),
            171 =>
            array(
                'name' => 'Paraguay',
                'iso3' => 'PRY',
                'iso2' => 'PY',
                'dial_code' => '595',
                'currency' => 'PYG',
                'currency_symbol' => '₲',
                'translations' => '{"br":"Paraguai","pt":"Paraguai","nl":"Paraguay","hr":"Paragvaj","fa":"پاراگوئه","de":"Paraguay","es":"Paraguay","fr":"Paraguay","ja":"パラグアイ","it":"Paraguay","ar":"باراغواي"}',
                'latitude' => '-23.00000000',
                'longitude' => '-58.00000000',
            ),
            172 =>
            array(
                'name' => 'Peru',
                'iso3' => 'PER',
                'iso2' => 'PE',
                'dial_code' => '51',
                'currency' => 'PEN',
                'currency_symbol' => 'S/.',
                'translations' => '{"br":"Peru","pt":"Peru","nl":"Peru","hr":"Peru","fa":"پرو","de":"Peru","es":"Perú","fr":"Pérou","ja":"ペルー","it":"Perù","ar":"بيرو"}',
                'latitude' => '-10.00000000',
                'longitude' => '-76.00000000',
            ),
            173 =>
            array(
                'name' => 'Philippines',
                'iso3' => 'PHL',
                'iso2' => 'PH',
                'dial_code' => '63',
                'currency' => 'PHP',
                'currency_symbol' => '₱',
                'translations' => '{"br":"Filipinas","pt":"Filipinas","nl":"Filipijnen","hr":"Filipini","fa":"جزایر الندفیلیپین","de":"Philippinen","es":"Filipinas","fr":"Philippines","ja":"フィリピン","it":"Filippine","ar":"الفلبين"}',
                'latitude' => '13.00000000',
                'longitude' => '122.00000000',
            ),
            174 =>
            array(
                'name' => 'Pitcairn Island',
                'iso3' => 'PCN',
                'iso2' => 'PN',
                'dial_code' => '870',
                'currency' => 'NZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Pitcairn","pt":"Ilhas Picárnia","nl":"Pitcairneilanden","hr":"Pitcairnovo otočje","fa":"پیتکرن","de":"Pitcairn","es":"Islas Pitcairn","fr":"Îles Pitcairn","ja":"ピトケアン","it":"Isole Pitcairn","ar":"جزيرة بيتكيرن"}',
                'latitude' => '-25.06666666',
                'longitude' => '-130.10000000',
            ),
            175 =>
            array(
                'name' => 'Poland',
                'iso3' => 'POL',
                'iso2' => 'PL',
                'dial_code' => '48',
                'currency' => 'PLN',
                'currency_symbol' => 'zł',
                'translations' => '{"br":"Polônia","pt":"Polónia","nl":"Polen","hr":"Poljska","fa":"لهستان","de":"Polen","es":"Polonia","fr":"Pologne","ja":"ポーランド","it":"Polonia","ar":"بولندا"}',
                'latitude' => '52.00000000',
                'longitude' => '20.00000000',
            ),
            176 =>
            array(
                'name' => 'Portugal',
                'iso3' => 'PRT',
                'iso2' => 'PT',
                'dial_code' => '351',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Portugal","pt":"Portugal","nl":"Portugal","hr":"Portugal","fa":"پرتغال","de":"Portugal","es":"Portugal","fr":"Portugal","ja":"ポルトガル","it":"Portogallo","ar":"البرتغال"}',
                'latitude' => '39.50000000',
                'longitude' => '-8.00000000',
            ),
            177 =>
            array(
                'name' => 'Puerto Rico',
                'iso3' => 'PRI',
                'iso2' => 'PR',
                'dial_code' => '+1-787 and 1-939',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Porto Rico","pt":"Porto Rico","nl":"Puerto Rico","hr":"Portoriko","fa":"پورتو ریکو","de":"Puerto Rico","es":"Puerto Rico","fr":"Porto Rico","ja":"プエルトリコ","it":"Porto Rico","ar":"بورتوريكو"}',
                'latitude' => '18.25000000',
                'longitude' => '-66.50000000',
            ),
            178 =>
            array(
                'name' => 'Qatar',
                'iso3' => 'QAT',
                'iso2' => 'QA',
                'dial_code' => '974',
                'currency' => 'QAR',
                'currency_symbol' => 'ق.ر',
                'translations' => '{"br":"Catar","pt":"Catar","nl":"Qatar","hr":"Katar","fa":"قطر","de":"Katar","es":"Catar","fr":"Qatar","ja":"カタール","it":"Qatar","ar":"قطر"}',
                'latitude' => '25.50000000',
                'longitude' => '51.25000000',
            ),
            179 =>
            array(
                'name' => 'Reunion',
                'iso3' => 'REU',
                'iso2' => 'RE',
                'dial_code' => '262',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Reunião","pt":"Reunião","nl":"Réunion","hr":"Réunion","fa":"رئونیون","de":"Réunion","es":"Reunión","fr":"Réunion","ja":"レユニオン","it":"Riunione","ar":"ريونيون"}',
                'latitude' => '-21.15000000',
                'longitude' => '55.50000000',
            ),
            180 =>
            array(
                'name' => 'Romania',
                'iso3' => 'ROU',
                'iso2' => 'RO',
                'dial_code' => '40',
                'currency' => 'RON',
                'currency_symbol' => 'lei',
                'translations' => '{"br":"Romênia","pt":"Roménia","nl":"Roemenië","hr":"Rumunjska","fa":"رومانی","de":"Rumänien","es":"Rumania","fr":"Roumanie","ja":"ルーマニア","it":"Romania","ar":"رومانيا"}',
                'latitude' => '46.00000000',
                'longitude' => '25.00000000',
            ),
            181 =>
            array(
                'name' => 'Russia',
                'iso3' => 'RUS',
                'iso2' => 'RU',
                'dial_code' => '7',
                'currency' => 'RUB',
                'currency_symbol' => '₽',
                'translations' => '{"br":"Rússia","pt":"Rússia","nl":"Rusland","hr":"Rusija","fa":"روسیه","de":"Russland","es":"Rusia","fr":"Russie","ja":"ロシア連邦","it":"Russia","ar":"روسيا"}',
                'latitude' => '60.00000000',
                'longitude' => '100.00000000',
            ),
            182 =>
            array(
                'name' => 'Rwanda',
                'iso3' => 'RWA',
                'iso2' => 'RW',
                'dial_code' => '250',
                'currency' => 'RWF',
                'currency_symbol' => 'FRw',
                'translations' => '{"br":"Ruanda","pt":"Ruanda","nl":"Rwanda","hr":"Ruanda","fa":"رواندا","de":"Ruanda","es":"Ruanda","fr":"Rwanda","ja":"ルワンダ","it":"Ruanda","ar":"رواندا"}',
                'latitude' => '-2.00000000',
                'longitude' => '30.00000000',
            ),
            183 =>
            array(
                'name' => 'Saint Helena',
                'iso3' => 'SHN',
                'iso2' => 'SH',
                'dial_code' => '290',
                'currency' => 'SHP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Santa Helena","pt":"Santa Helena","nl":"Sint-Helena","hr":"Sveta Helena","fa":"سنت هلنا، اسنشن و تریستان دا کونا","de":"Sankt Helena","es":"Santa Helena","fr":"Sainte-Hélène","ja":"セントヘレナ・アセンションおよびトリスタンダクーニャ","it":"Sant\'Elena","ar":"سانت هيلينا"}',
                'latitude' => '-15.95000000',
                'longitude' => '-5.70000000',
            ),
            184 =>
            array(
                'name' => 'Saint Kitts And Nevis',
                'iso3' => 'KNA',
                'iso2' => 'KN',
                'dial_code' => '+1-869',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"São Cristóvão e Neves","pt":"São Cristóvão e Neves","nl":"Saint Kitts en Nevis","hr":"Sveti Kristof i Nevis","fa":"سنت کیتس و نویس","de":"St. Kitts und Nevis","es":"San Cristóbal y Nieves","fr":"Saint-Christophe-et-Niévès","ja":"セントクリストファー・ネイビス","it":"Saint Kitts e Nevis","ar":"سانت كيتس ونيفيس"}',
                'latitude' => '17.33333333',
                'longitude' => '-62.75000000',
            ),
            185 =>
            array(
                'name' => 'Saint Lucia',
                'iso3' => 'LCA',
                'iso2' => 'LC',
                'dial_code' => '+1-758',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Santa Lúcia","pt":"Santa Lúcia","nl":"Saint Lucia","hr":"Sveta Lucija","fa":"سنت لوسیا","de":"Saint Lucia","es":"Santa Lucía","fr":"Saint-Lucie","ja":"セントルシア","it":"Santa Lucia","ar":"سانت لوسيا"}',
                'latitude' => '13.88333333',
                'longitude' => '-60.96666666',
            ),
            186 =>
            array(
                'name' => 'Saint Pierre and Miquelon',
                'iso3' => 'SPM',
                'iso2' => 'PM',
                'dial_code' => '508',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Saint-Pierre e Miquelon","pt":"São Pedro e Miquelon","nl":"Saint Pierre en Miquelon","hr":"Sveti Petar i Mikelon","fa":"سن پیر و میکلن","de":"Saint-Pierre und Miquelon","es":"San Pedro y Miquelón","fr":"Saint-Pierre-et-Miquelon","ja":"サンピエール島・ミクロン島","it":"Saint-Pierre e Miquelon","ar":"سان بيير وميكلون"}',
                'latitude' => '46.83333333',
                'longitude' => '-56.33333333',
            ),
            187 =>
            array(
                'name' => 'Saint Vincent And The Grenadines',
                'iso3' => 'VCT',
                'iso2' => 'VC',
                'dial_code' => '+1-784',
                'currency' => 'XCD',
                'currency_symbol' => '$',
                'translations' => '{"br":"São Vicente e Granadinas","pt":"São Vicente e Granadinas","nl":"Saint Vincent en de Grenadines","hr":"Sveti Vincent i Grenadini","fa":"سنت وینسنت و گرنادین‌ها","de":"Saint Vincent und die Grenadinen","es":"San Vicente y Granadinas","fr":"Saint-Vincent-et-les-Grenadines","ja":"セントビンセントおよびグレナディーン諸島","it":"Saint Vincent e Grenadine","ar":"سانت فنسنت وجزر غرينادين"}',
                'latitude' => '13.25000000',
                'longitude' => '-61.20000000',
            ),
            188 =>
            array(
                'name' => 'Saint-Barthelemy',
                'iso3' => 'BLM',
                'iso2' => 'BL',
                'dial_code' => '590',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"São Bartolomeu","pt":"São Bartolomeu","nl":"Saint Barthélemy","hr":"Saint Barthélemy","fa":"سن-بارتلمی","de":"Saint-Barthélemy","es":"San Bartolomé","fr":"Saint-Barthélemy","ja":"サン・バルテルミー","it":"Antille Francesi","ar":"سان بارتيليمي"}',
                'latitude' => '18.50000000',
                'longitude' => '-63.41666666',
            ),
            189 =>
            array(
                'name' => 'Saint-Martin (French part)',
                'iso3' => 'MAF',
                'iso2' => 'MF',
                'dial_code' => '590',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Saint Martin","pt":"Ilha São Martinho","nl":"Saint-Martin","hr":"Sveti Martin","fa":"سینت مارتن","de":"Saint Martin","es":"Saint Martin","fr":"Saint-Martin","ja":"サン・マルタン（フランス領）","it":"Saint Martin","ar":"سان مارتن"}',
                'latitude' => '18.08333333',
                'longitude' => '-63.95000000',
            ),
            190 =>
            array(
                'name' => 'Samoa',
                'iso3' => 'WSM',
                'iso2' => 'WS',
                'dial_code' => '685',
                'currency' => 'WST',
                'currency_symbol' => 'SAT',
                'translations' => '{"br":"Samoa","pt":"Samoa","nl":"Samoa","hr":"Samoa","fa":"ساموآ","de":"Samoa","es":"Samoa","fr":"Samoa","ja":"サモア","it":"Samoa","ar":"ساموا"}',
                'latitude' => '-13.58333333',
                'longitude' => '-172.33333333',
            ),
            191 =>
            array(
                'name' => 'San Marino',
                'iso3' => 'SMR',
                'iso2' => 'SM',
                'dial_code' => '378',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"San Marino","pt":"São Marinho","nl":"San Marino","hr":"San Marino","fa":"سان مارینو","de":"San Marino","es":"San Marino","fr":"Saint-Marin","ja":"サンマリノ","it":"San Marino","ar":"سان مارينو"}',
                'latitude' => '43.76666666',
                'longitude' => '12.41666666',
            ),
            192 =>
            array(
                'name' => 'Sao Tome and Principe',
                'iso3' => 'STP',
                'iso2' => 'ST',
                'dial_code' => '239',
                'currency' => 'STD',
                'currency_symbol' => 'Db',
                'translations' => '{"br":"São Tomé e Príncipe","pt":"São Tomé e Príncipe","nl":"Sao Tomé en Principe","hr":"Sveti Toma i Princip","fa":"کواترو دو فرویرو","de":"São Tomé und Príncipe","es":"Santo Tomé y Príncipe","fr":"Sao Tomé-et-Principe","ja":"サントメ・プリンシペ","it":"São Tomé e Príncipe","ar":"ساو تومي وبرينسيبي"}',
                'latitude' => '1.00000000',
                'longitude' => '7.00000000',
            ),
            193 =>
            array(
                'name' => 'Saudi Arabia',
                'iso3' => 'SAU',
                'iso2' => 'SA',
                'dial_code' => '966',
                'currency' => 'SAR',
                'currency_symbol' => '﷼',
                'translations' => '{"br":"Arábia Saudita","pt":"Arábia Saudita","nl":"Saoedi-Arabië","hr":"Saudijska Arabija","fa":"عربستان سعودی","de":"Saudi-Arabien","es":"Arabia Saudí","fr":"Arabie Saoudite","ja":"サウジアラビア","it":"Arabia Saudita","ar":"المملكة العربية السعودية"}',
                'latitude' => '25.00000000',
                'longitude' => '45.00000000',
            ),
            194 =>
            array(
                'name' => 'Senegal',
                'iso3' => 'SEN',
                'iso2' => 'SN',
                'dial_code' => '221',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Senegal","pt":"Senegal","nl":"Senegal","hr":"Senegal","fa":"سنگال","de":"Senegal","es":"Senegal","fr":"Sénégal","ja":"セネガル","it":"Senegal","ar":"السنغال"}',
                'latitude' => '14.00000000',
                'longitude' => '-14.00000000',
            ),
            195 =>
            array(
                'name' => 'Serbia',
                'iso3' => 'SRB',
                'iso2' => 'RS',
                'dial_code' => '381',
                'currency' => 'RSD',
                'currency_symbol' => 'din',
                'translations' => '{"br":"Sérvia","pt":"Sérvia","nl":"Servië","hr":"Srbija","fa":"صربستان","de":"Serbien","es":"Serbia","fr":"Serbie","ja":"セルビア","it":"Serbia","ar":"صربيا"}',
                'latitude' => '44.00000000',
                'longitude' => '21.00000000',
            ),
            196 =>
            array(
                'name' => 'Seychelles',
                'iso3' => 'SYC',
                'iso2' => 'SC',
                'dial_code' => '248',
                'currency' => 'SCR',
                'currency_symbol' => 'SRe',
                'translations' => '{"br":"Seicheles","pt":"Seicheles","nl":"Seychellen","hr":"Sejšeli","fa":"سیشل","de":"Seychellen","es":"Seychelles","fr":"Seychelles","ja":"セーシェル","it":"Seychelles","ar":"سيشل"}',
                'latitude' => '-4.58333333',
                'longitude' => '55.66666666',
            ),
            197 =>
            array(
                'name' => 'Sierra Leone',
                'iso3' => 'SLE',
                'iso2' => 'SL',
                'dial_code' => '232',
                'currency' => 'SLL',
                'currency_symbol' => 'Le',
                'translations' => '{"br":"Serra Leoa","pt":"Serra Leoa","nl":"Sierra Leone","hr":"Sijera Leone","fa":"سیرالئون","de":"Sierra Leone","es":"Sierra Leone","fr":"Sierra Leone","ja":"シエラレオネ","it":"Sierra Leone","ar":"سيراليون"}',
                'latitude' => '8.50000000',
                'longitude' => '-11.50000000',
            ),
            198 =>
            array(
                'name' => 'Singapore',
                'iso3' => 'SGP',
                'iso2' => 'SG',
                'dial_code' => '65',
                'currency' => 'SGD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Singapura","pt":"Singapura","nl":"Singapore","hr":"Singapur","fa":"سنگاپور","de":"Singapur","es":"Singapur","fr":"Singapour","ja":"シンガポール","it":"Singapore","ar":"سنغافورة"}',
                'latitude' => '1.36666666',
                'longitude' => '103.80000000',
            ),
            199 =>
            array(
                'name' => 'Slovakia',
                'iso3' => 'SVK',
                'iso2' => 'SK',
                'dial_code' => '421',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Eslováquia","pt":"Eslováquia","nl":"Slowakije","hr":"Slovačka","fa":"اسلواکی","de":"Slowakei","es":"República Eslovaca","fr":"Slovaquie","ja":"スロバキア","it":"Slovacchia","ar":"سلوفاكيا"}',
                'latitude' => '48.66666666',
                'longitude' => '19.50000000',
            ),
            200 =>
            array(
                'name' => 'Slovenia',
                'iso3' => 'SVN',
                'iso2' => 'SI',
                'dial_code' => '386',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Eslovênia","pt":"Eslovénia","nl":"Slovenië","hr":"Slovenija","fa":"اسلوونی","de":"Slowenien","es":"Eslovenia","fr":"Slovénie","ja":"スロベニア","it":"Slovenia","ar":"سلوفينيا"}',
                'latitude' => '46.11666666',
                'longitude' => '14.81666666',
            ),
            201 =>
            array(
                'name' => 'Solomon Islands',
                'iso3' => 'SLB',
                'iso2' => 'SB',
                'dial_code' => '677',
                'currency' => 'SBD',
                'currency_symbol' => 'Si$',
                'translations' => '{"br":"Ilhas Salomão","pt":"Ilhas Salomão","nl":"Salomonseilanden","hr":"Solomonski Otoci","fa":"جزایر سلیمان","de":"Salomonen","es":"Islas Salomón","fr":"Îles Salomon","ja":"ソロモン諸島","it":"Isole Salomone","ar":"جزر سليمان"}',
                'latitude' => '-8.00000000',
                'longitude' => '159.00000000',
            ),
            202 =>
            array(
                'name' => 'Somalia',
                'iso3' => 'SOM',
                'iso2' => 'SO',
                'dial_code' => '252',
                'currency' => 'SOS',
                'currency_symbol' => 'Sh.so.',
                'translations' => '{"br":"Somália","pt":"Somália","nl":"Somalië","hr":"Somalija","fa":"سومالی","de":"Somalia","es":"Somalia","fr":"Somalie","ja":"ソマリア","it":"Somalia","ar":"الصومال"}',
                'latitude' => '10.00000000',
                'longitude' => '49.00000000',
            ),
            203 =>
            array(
                'name' => 'South Africa',
                'iso3' => 'ZAF',
                'iso2' => 'ZA',
                'dial_code' => '27',
                'currency' => 'ZAR',
                'currency_symbol' => 'R',
                'translations' => '{"br":"República Sul-Africana","pt":"República Sul-Africana","nl":"Zuid-Afrika","hr":"Južnoafrička Republika","fa":"آفریقای جنوبی","de":"Republik Südafrika","es":"República de Sudáfrica","fr":"Afrique du Sud","ja":"南アフリカ","it":"Sud Africa","ar":"جنوب أفريقيا"}',
                'latitude' => '-29.00000000',
                'longitude' => '24.00000000',
            ),
            204 =>
            array(
                'name' => 'South Georgia',
                'iso3' => 'SGS',
                'iso2' => 'GS',
                'dial_code' => '',
                'currency' => 'GBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Ilhas Geórgias do Sul e Sandwich do Sul","pt":"Ilhas Geórgia do Sul e Sanduíche do Sul","nl":"Zuid-Georgia en Zuidelijke Sandwicheilanden","hr":"Južna Georgija i otočje Južni Sandwich","fa":"جزایر جورجیای جنوبی و ساندویچ جنوبی","de":"Südgeorgien und die Südlichen Sandwichinseln","es":"Islas Georgias del Sur y Sandwich del Sur","fr":"Géorgie du Sud-et-les Îles Sandwich du Sud","ja":"サウスジョージア・サウスサンドウィッチ諸島","it":"Georgia del Sud e Isole Sandwich Meridionali","ar":"جورجيا الجنوبية"}',
                'latitude' => '-54.50000000',
                'longitude' => '-37.00000000',
            ),
            205 =>
            array(
                'name' => 'South Sudan',
                'iso3' => 'SSD',
                'iso2' => 'SS',
                'dial_code' => '211',
                'currency' => 'SSP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Sudão do Sul","pt":"Sudão do Sul","nl":"Zuid-Soedan","hr":"Južni Sudan","fa":"سودان جنوبی","de":"Südsudan","es":"Sudán del Sur","fr":"Soudan du Sud","ja":"南スーダン","it":"Sudan del sud","ar":"جنوب السودان"}',
                'latitude' => '7.00000000',
                'longitude' => '30.00000000',
            ),
            206 =>
            array(
                'name' => 'Spain',
                'iso3' => 'ESP',
                'iso2' => 'ES',
                'dial_code' => '34',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Espanha","pt":"Espanha","nl":"Spanje","hr":"Španjolska","fa":"اسپانیا","de":"Spanien","es":"España","fr":"Espagne","ja":"スペイン","it":"Spagna","ar":"إسبانيا"}',
                'latitude' => '40.00000000',
                'longitude' => '-4.00000000',
            ),
            207 =>
            array(
                'name' => 'Sri Lanka',
                'iso3' => 'LKA',
                'iso2' => 'LK',
                'dial_code' => '94',
                'currency' => 'LKR',
                'currency_symbol' => 'Rs',
                'translations' => '{"br":"Sri Lanka","pt":"Sri Lanka","nl":"Sri Lanka","hr":"Šri Lanka","fa":"سری‌لانکا","de":"Sri Lanka","es":"Sri Lanka","fr":"Sri Lanka","ja":"スリランカ","it":"Sri Lanka","ar":"سريلانكا"}',
                'latitude' => '7.00000000',
                'longitude' => '81.00000000',
            ),
            208 =>
            array(
                'name' => 'Sudan',
                'iso3' => 'SDN',
                'iso2' => 'SD',
                'dial_code' => '249',
                'currency' => 'SDG',
                'currency_symbol' => '.س.ج',
                'translations' => '{"br":"Sudão","pt":"Sudão","nl":"Soedan","hr":"Sudan","fa":"سودان","de":"Sudan","es":"Sudán","fr":"Soudan","ja":"スーダン","it":"Sudan","ar":"السودان"}',
                'latitude' => '15.00000000',
                'longitude' => '30.00000000',
            ),
            209 =>
            array(
                'name' => 'Suriname',
                'iso3' => 'SUR',
                'iso2' => 'SR',
                'dial_code' => '597',
                'currency' => 'SRD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Suriname","pt":"Suriname","nl":"Suriname","hr":"Surinam","fa":"سورینام","de":"Suriname","es":"Surinam","fr":"Surinam","ja":"スリナム","it":"Suriname","ar":"سورينام"}',
                'latitude' => '4.00000000',
                'longitude' => '-56.00000000',
            ),
            210 =>
            array(
                'name' => 'Svalbard And Jan Mayen Islands',
                'iso3' => 'SJM',
                'iso2' => 'SJ',
                'dial_code' => '47',
                'currency' => 'NOK',
                'currency_symbol' => 'kr',
                'translations' => '{"br":"Svalbard","pt":"Svalbard","nl":"Svalbard en Jan Mayen","hr":"Svalbard i Jan Mayen","fa":"سوالبارد و یان ماین","de":"Svalbard und Jan Mayen","es":"Islas Svalbard y Jan Mayen","fr":"Svalbard et Jan Mayen","ja":"スヴァールバル諸島およびヤンマイエン島","it":"Svalbard e Jan Mayen","ar":"سفالبارد وجان ماين"}',
                'latitude' => '78.00000000',
                'longitude' => '20.00000000',
            ),
            211 =>
            array(
                'name' => 'Swaziland',
                'iso3' => 'SWZ',
                'iso2' => 'SZ',
                'dial_code' => '268',
                'currency' => 'SZL',
                'currency_symbol' => 'E',
                'translations' => '{"br":"Suazilândia","pt":"Suazilândia","nl":"Swaziland","hr":"Svazi","fa":"سوازیلند","de":"Swasiland","es":"Suazilandia","fr":"Swaziland","ja":"スワジランド","it":"Swaziland","ar":"سوازيلاند"}',
                'latitude' => '-26.50000000',
                'longitude' => '31.50000000',
            ),
            212 =>
            array(
                'name' => 'Sweden',
                'iso3' => 'SWE',
                'iso2' => 'SE',
                'dial_code' => '46',
                'currency' => 'SEK',
                'currency_symbol' => 'kr',
                'translations' => '{"br":"Suécia","pt":"Suécia","nl":"Zweden","hr":"Švedska","fa":"سوئد","de":"Schweden","es":"Suecia","fr":"Suède","ja":"スウェーデン","it":"Svezia","ar":"السويد"}',
                'latitude' => '62.00000000',
                'longitude' => '15.00000000',
            ),
            213 =>
            array(
                'name' => 'Switzerland',
                'iso3' => 'CHE',
                'iso2' => 'CH',
                'dial_code' => '41',
                'currency' => 'CHF',
                'currency_symbol' => 'CHf',
                'translations' => '{"br":"Suíça","pt":"Suíça","nl":"Zwitserland","hr":"Švicarska","fa":"سوئیس","de":"Schweiz","es":"Suiza","fr":"Suisse","ja":"スイス","it":"Svizzera","ar":"سويسرا"}',
                'latitude' => '47.00000000',
                'longitude' => '8.00000000',
            ),
            214 =>
            array(
                'name' => 'Syria',
                'iso3' => 'SYR',
                'iso2' => 'SY',
                'dial_code' => '963',
                'currency' => 'SYP',
                'currency_symbol' => 'LS',
                'translations' => '{"br":"Síria","pt":"Síria","nl":"Syrië","hr":"Sirija","fa":"سوریه","de":"Syrien","es":"Siria","fr":"Syrie","ja":"シリア・アラブ共和国","it":"Siria","ar":"سوريا"}',
                'latitude' => '35.00000000',
                'longitude' => '38.00000000',
            ),
            215 =>
            array(
                'name' => 'Taiwan',
                'iso3' => 'TWN',
                'iso2' => 'TW',
                'dial_code' => '886',
                'currency' => 'TWD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Taiwan","pt":"Taiwan","nl":"Taiwan","hr":"Tajvan","fa":"تایوان","de":"Taiwan","es":"Taiwán","fr":"Taïwan","ja":"台湾（中華民国）","it":"Taiwan","ar":"تايوان"}',
                'latitude' => '23.50000000',
                'longitude' => '121.00000000',
            ),
            216 =>
            array(
                'name' => 'Tajikistan',
                'iso3' => 'TJK',
                'iso2' => 'TJ',
                'dial_code' => '992',
                'currency' => 'TJS',
                'currency_symbol' => 'SM',
                'translations' => '{"br":"Tajiquistão","pt":"Tajiquistão","nl":"Tadzjikistan","hr":"Tađikistan","fa":"تاجیکستان","de":"Tadschikistan","es":"Tayikistán","fr":"Tadjikistan","ja":"タジキスタン","it":"Tagikistan","ar":"طاجيكستان"}',
                'latitude' => '39.00000000',
                'longitude' => '71.00000000',
            ),
            217 =>
            array(
                'name' => 'Tanzania',
                'iso3' => 'TZA',
                'iso2' => 'TZ',
                'dial_code' => '255',
                'currency' => 'TZS',
                'currency_symbol' => 'TSh',
                'translations' => '{"br":"Tanzânia","pt":"Tanzânia","nl":"Tanzania","hr":"Tanzanija","fa":"تانزانیا","de":"Tansania","es":"Tanzania","fr":"Tanzanie","ja":"タンザニア","it":"Tanzania","ar":"تنزانيا"}',
                'latitude' => '-6.00000000',
                'longitude' => '35.00000000',
            ),
            218 =>
            array(
                'name' => 'Thailand',
                'iso3' => 'THA',
                'iso2' => 'TH',
                'dial_code' => '66',
                'currency' => 'THB',
                'currency_symbol' => '฿',
                'translations' => '{"br":"Tailândia","pt":"Tailândia","nl":"Thailand","hr":"Tajland","fa":"تایلند","de":"Thailand","es":"Tailandia","fr":"Thaïlande","ja":"タイ","it":"Tailandia","ar":"تايلاند"}',
                'latitude' => '15.00000000',
                'longitude' => '100.00000000',
            ),
            219 =>
            array(
                'name' => 'Togo',
                'iso3' => 'TGO',
                'iso2' => 'TG',
                'dial_code' => '228',
                'currency' => 'XOF',
                'currency_symbol' => 'CFA',
                'translations' => '{"br":"Togo","pt":"Togo","nl":"Togo","hr":"Togo","fa":"توگو","de":"Togo","es":"Togo","fr":"Togo","ja":"トーゴ","it":"Togo","ar":"توغو"}',
                'latitude' => '8.00000000',
                'longitude' => '1.16666666',
            ),
            220 =>
            array(
                'name' => 'Tokelau',
                'iso3' => 'TKL',
                'iso2' => 'TK',
                'dial_code' => '690',
                'currency' => 'NZD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Tokelau","pt":"Toquelau","nl":"Tokelau","hr":"Tokelau","fa":"توکلائو","de":"Tokelau","es":"Islas Tokelau","fr":"Tokelau","ja":"トケラウ","it":"Isole Tokelau","ar":"توكيلاو"}',
                'latitude' => '-9.00000000',
                'longitude' => '-172.00000000',
            ),
            221 =>
            array(
                'name' => 'Tonga',
                'iso3' => 'TON',
                'iso2' => 'TO',
                'dial_code' => '676',
                'currency' => 'TOP',
                'currency_symbol' => '$',
                'translations' => '{"br":"Tonga","pt":"Tonga","nl":"Tonga","hr":"Tonga","fa":"تونگا","de":"Tonga","es":"Tonga","fr":"Tonga","ja":"トンガ","it":"Tonga","ar":"تونغا"}',
                'latitude' => '-20.00000000',
                'longitude' => '-175.00000000',
            ),
            222 =>
            array(
                'name' => 'Trinidad And Tobago',
                'iso3' => 'TTO',
                'iso2' => 'TT',
                'dial_code' => '+1-868',
                'currency' => 'TTD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Trinidad e Tobago","pt":"Trindade e Tobago","nl":"Trinidad en Tobago","hr":"Trinidad i Tobago","fa":"ترینیداد و توباگو","de":"Trinidad und Tobago","es":"Trinidad y Tobago","fr":"Trinité et Tobago","ja":"トリニダード・トバゴ","it":"Trinidad e Tobago","ar":"ترينيداد وتوباغو"}',
                'latitude' => '11.00000000',
                'longitude' => '-61.00000000',
            ),
            223 =>
            array(
                'name' => 'Tunisia',
                'iso3' => 'TUN',
                'iso2' => 'TN',
                'dial_code' => '216',
                'currency' => 'TND',
                'currency_symbol' => 'ت.د',
                'translations' => '{"br":"Tunísia","pt":"Tunísia","nl":"Tunesië","hr":"Tunis","fa":"تونس","de":"Tunesien","es":"Túnez","fr":"Tunisie","ja":"チュニジア","it":"Tunisia","ar":"تونس"}',
                'latitude' => '34.00000000',
                'longitude' => '9.00000000',
            ),
            224 =>
            array(
                'name' => 'Turkey',
                'iso3' => 'TUR',
                'iso2' => 'TR',
                'dial_code' => '90',
                'currency' => 'TRY',
                'currency_symbol' => '₺',
                'translations' => '{"br":"Turquia","pt":"Turquia","nl":"Turkije","hr":"Turska","fa":"ترکیه","de":"Türkei","es":"Turquía","fr":"Turquie","ja":"トルコ","it":"Turchia","ar":"تركيا"}',
                'latitude' => '39.00000000',
                'longitude' => '35.00000000',
            ),
            225 =>
            array(
                'name' => 'Turkmenistan',
                'iso3' => 'TKM',
                'iso2' => 'TM',
                'dial_code' => '993',
                'currency' => 'TMT',
                'currency_symbol' => 'T',
                'translations' => '{"br":"Turcomenistão","pt":"Turquemenistão","nl":"Turkmenistan","hr":"Turkmenistan","fa":"ترکمنستان","de":"Turkmenistan","es":"Turkmenistán","fr":"Turkménistan","ja":"トルクメニスタン","it":"Turkmenistan","ar":"تركمانستان"}',
                'latitude' => '40.00000000',
                'longitude' => '60.00000000',
            ),
            226 =>
            array(
                'name' => 'Turks And Caicos Islands',
                'iso3' => 'TCA',
                'iso2' => 'TC',
                'dial_code' => '+1-649',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Turcas e Caicos","pt":"Ilhas Turcas e Caicos","nl":"Turks- en Caicoseilanden","hr":"Otoci Turks i Caicos","fa":"جزایر تورکس و کایکوس","de":"Turks- und Caicosinseln","es":"Islas Turks y Caicos","fr":"Îles Turques-et-Caïques","ja":"タークス・カイコス諸島","it":"Isole Turks e Caicos","ar":"جزر تركس وكايكوس"}',
                'latitude' => '21.75000000',
                'longitude' => '-71.58333333',
            ),
            227 =>
            array(
                'name' => 'Tuvalu',
                'iso3' => 'TUV',
                'iso2' => 'TV',
                'dial_code' => '688',
                'currency' => 'AUD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Tuvalu","pt":"Tuvalu","nl":"Tuvalu","hr":"Tuvalu","fa":"تووالو","de":"Tuvalu","es":"Tuvalu","fr":"Tuvalu","ja":"ツバル","it":"Tuvalu","ar":"توفالو"}',
                'latitude' => '-8.00000000',
                'longitude' => '178.00000000',
            ),
            228 =>
            array(
                'name' => 'Uganda',
                'iso3' => 'UGA',
                'iso2' => 'UG',
                'dial_code' => '256',
                'currency' => 'UGX',
                'currency_symbol' => 'USh',
                'translations' => '{"br":"Uganda","pt":"Uganda","nl":"Oeganda","hr":"Uganda","fa":"اوگاندا","de":"Uganda","es":"Uganda","fr":"Uganda","ja":"ウガンダ","it":"Uganda","ar":"أوغندا"}',
                'latitude' => '1.00000000',
                'longitude' => '32.00000000',
            ),
            229 =>
            array(
                'name' => 'Ukraine',
                'iso3' => 'UKR',
                'iso2' => 'UA',
                'dial_code' => '380',
                'currency' => 'UAH',
                'currency_symbol' => '₴',
                'translations' => '{"br":"Ucrânia","pt":"Ucrânia","nl":"Oekraïne","hr":"Ukrajina","fa":"وکراین","de":"Ukraine","es":"Ucrania","fr":"Ukraine","ja":"ウクライナ","it":"Ucraina","ar":"أوكرانيا"}',
                'latitude' => '49.00000000',
                'longitude' => '32.00000000',
            ),
            230 =>
            array(
                'name' => 'United Arab Emirates',
                'iso3' => 'ARE',
                'iso2' => 'AE',
                'dial_code' => '971',
                'currency' => 'AED',
                'currency_symbol' => 'إ.د',
                'translations' => '{"br":"Emirados árabes Unidos","pt":"Emirados árabes Unidos","nl":"Verenigde Arabische Emiraten","hr":"Ujedinjeni Arapski Emirati","fa":"امارات متحده عربی","de":"Vereinigte Arabische Emirate","es":"Emiratos Árabes Unidos","fr":"Émirats arabes unis","ja":"アラブ首長国連邦","it":"Emirati Arabi Uniti","ar":"الإمارات العربية المتحدة"}',
                'latitude' => '24.00000000',
                'longitude' => '54.00000000',
            ),
            231 =>
            array(
                'name' => 'United Kingdom',
                'iso3' => 'GBR',
                'iso2' => 'GB',
                'dial_code' => '44',
                'currency' => 'GBP',
                'currency_symbol' => '£',
                'translations' => '{"br":"Reino Unido","pt":"Reino Unido","nl":"Verenigd Koninkrijk","hr":"Ujedinjeno Kraljevstvo","fa":"بریتانیای کبیر و ایرلند شمالی","de":"Vereinigtes Königreich","es":"Reino Unido","fr":"Royaume-Uni","ja":"イギリス","it":"Regno Unito","ar":"المملكة المتحدة"}',
                'latitude' => '54.00000000',
                'longitude' => '-2.00000000',
            ),
            232 =>
            array(
                'name' => 'United States',
                'iso3' => 'USA',
                'iso2' => 'US',
                'dial_code' => '1',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Estados Unidos","pt":"Estados Unidos","nl":"Verenigde Staten","hr":"Sjedinjene Američke Države","fa":"ایالات متحده آمریکا","de":"Vereinigte Staaten von Amerika","es":"Estados Unidos","fr":"États-Unis","ja":"アメリカ合衆国","it":"Stati Uniti D\'America","ar":"الولايات المتحدة"}',
                'latitude' => '38.00000000',
                'longitude' => '-97.00000000',
            ),
            233 =>
            array(
                'name' => 'United States Minor Outlying Islands',
                'iso3' => 'UMI',
                'iso2' => 'UM',
                'dial_code' => '1',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Menores Distantes dos Estados Unidos","pt":"Ilhas Menores Distantes dos Estados Unidos","nl":"Kleine afgelegen eilanden van de Verenigde Staten","hr":"Mali udaljeni otoci SAD-a","fa":"جزایر کوچک حاشیه‌ای ایالات متحده آمریکا","de":"Kleinere Inselbesitzungen der Vereinigten Staaten","es":"Islas Ultramarinas Menores de Estados Unidos","fr":"Îles mineures éloignées des États-Unis","ja":"合衆国領有小離島","it":"Isole minori esterne degli Stati Uniti d\'America","ar":"جزر الولايات المتحدة النائية"}',
                'latitude' => '0.00000000',
                'longitude' => '0.00000000',
            ),
            234 =>
            array(
                'name' => 'Uruguay',
                'iso3' => 'URY',
                'iso2' => 'UY',
                'dial_code' => '598',
                'currency' => 'UYU',
                'currency_symbol' => '$',
                'translations' => '{"br":"Uruguai","pt":"Uruguai","nl":"Uruguay","hr":"Urugvaj","fa":"اروگوئه","de":"Uruguay","es":"Uruguay","fr":"Uruguay","ja":"ウルグアイ","it":"Uruguay","ar":"أوروغواي"}',
                'latitude' => '-33.00000000',
                'longitude' => '-56.00000000',
            ),
            235 =>
            array(
                'name' => 'Uzbekistan',
                'iso3' => 'UZB',
                'iso2' => 'UZ',
                'dial_code' => '998',
                'currency' => 'UZS',
                'currency_symbol' => 'лв',
                'translations' => '{"br":"Uzbequistão","pt":"Usbequistão","nl":"Oezbekistan","hr":"Uzbekistan","fa":"ازبکستان","de":"Usbekistan","es":"Uzbekistán","fr":"Ouzbékistan","ja":"ウズベキスタン","it":"Uzbekistan","ar":"أوزبكستان"}',
                'latitude' => '41.00000000',
                'longitude' => '64.00000000',
            ),
            236 =>
            array(
                'name' => 'Vanuatu',
                'iso3' => 'VUT',
                'iso2' => 'VU',
                'dial_code' => '678',
                'currency' => 'VUV',
                'currency_symbol' => 'VT',
                'translations' => '{"br":"Vanuatu","pt":"Vanuatu","nl":"Vanuatu","hr":"Vanuatu","fa":"وانواتو","de":"Vanuatu","es":"Vanuatu","fr":"Vanuatu","ja":"バヌアツ","it":"Vanuatu","ar":"فانواتو"}',
                'latitude' => '-16.00000000',
                'longitude' => '167.00000000',
            ),
            237 =>
            array(
                'name' => 'Vatican City State (Holy See)',
                'iso3' => 'VAT',
                'iso2' => 'VA',
                'dial_code' => '379',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"br":"Vaticano","pt":"Vaticano","nl":"Heilige Stoel","hr":"Sveta Stolica","fa":"سریر مقدس","de":"Heiliger Stuhl","es":"Santa Sede","fr":"voir Saint","ja":"聖座","it":"Santa Sede","ar":"دولة مدينة الفاتيكان"}',
                'latitude' => '41.90000000',
                'longitude' => '12.45000000',
            ),
            238 =>
            array(
                'name' => 'Venezuela',
                'iso3' => 'VEN',
                'iso2' => 'VE',
                'dial_code' => '58',
                'currency' => 'VEF',
                'currency_symbol' => 'Bs',
                'translations' => '{"br":"Venezuela","pt":"Venezuela","nl":"Venezuela","hr":"Venezuela","fa":"ونزوئلا","de":"Venezuela","es":"Venezuela","fr":"Venezuela","ja":"ベネズエラ・ボリバル共和国","it":"Venezuela","ar":"فنزويلا"}',
                'latitude' => '8.00000000',
                'longitude' => '-66.00000000',
            ),
            239 =>
            array(
                'name' => 'Vietnam',
                'iso3' => 'VNM',
                'iso2' => 'VN',
                'dial_code' => '84',
                'currency' => 'VND',
                'currency_symbol' => '₫',
                'translations' => '{"br":"Vietnã","pt":"Vietname","nl":"Vietnam","hr":"Vijetnam","fa":"ویتنام","de":"Vietnam","es":"Vietnam","fr":"Viêt Nam","ja":"ベトナム","it":"Vietnam","ar":"فيتنام"}',
                'latitude' => '16.16666666',
                'longitude' => '107.83333333',
            ),
            240 =>
            array(
                'name' => 'Virgin Islands (British)',
                'iso3' => 'VGB',
                'iso2' => 'VG',
                'dial_code' => '+1-284',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Virgens Britânicas","pt":"Ilhas Virgens Britânicas","nl":"Britse Maagdeneilanden","hr":"Britanski Djevičanski Otoci","fa":"جزایر ویرجین بریتانیا","de":"Britische Jungferninseln","es":"Islas Vírgenes del Reino Unido","fr":"Îles Vierges britanniques","ja":"イギリス領ヴァージン諸島","it":"Isole Vergini Britanniche","ar":"جزر العذراء البريطانية"}',
                'latitude' => '18.43138300',
                'longitude' => '-64.62305000',
            ),
            241 =>
            array(
                'name' => 'Virgin Islands (US)',
                'iso3' => 'VIR',
                'iso2' => 'VI',
                'dial_code' => '+1-340',
                'currency' => 'USD',
                'currency_symbol' => '$',
                'translations' => '{"br":"Ilhas Virgens Americanas","pt":"Ilhas Virgens Americanas","nl":"Verenigde Staten Maagdeneilanden","fa":"جزایر ویرجین آمریکا","de":"Amerikanische Jungferninseln","es":"Islas Vírgenes de los Estados Unidos","fr":"Îles Vierges des États-Unis","ja":"アメリカ領ヴァージン諸島","it":"Isole Vergini americane","ar":"جزر العذراء الأمريكية"}',
                'latitude' => '18.34000000',
                'longitude' => '-64.93000000',
            ),
            242 =>
            array(
                'name' => 'Wallis And Futuna Islands',
                'iso3' => 'WLF',
                'iso2' => 'WF',
                'dial_code' => '681',
                'currency' => 'XPF',
                'currency_symbol' => '₣',
                'translations' => '{"br":"Wallis e Futuna","pt":"Wallis e Futuna","nl":"Wallis en Futuna","hr":"Wallis i Fortuna","fa":"والیس و فوتونا","de":"Wallis und Futuna","es":"Wallis y Futuna","fr":"Wallis-et-Futuna","ja":"ウォリス・フツナ","it":"Wallis e Futuna","ar":"جزر واليس وفوتونا"}',
                'latitude' => '-13.30000000',
                'longitude' => '-176.20000000',
            ),
            243 =>
            array(
                'name' => 'Western Sahara',
                'iso3' => 'ESH',
                'iso2' => 'EH',
                'dial_code' => '212',
                'currency' => 'MAD',
                'currency_symbol' => 'MAD',
                'translations' => '{"br":"Saara Ocidental","pt":"Saara Ocidental","nl":"Westelijke Sahara","hr":"Zapadna Sahara","fa":"جمهوری دموکراتیک عربی صحرا","de":"Westsahara","es":"Sahara Occidental","fr":"Sahara Occidental","ja":"西サハラ","it":"Sahara Occidentale","ar":"الصحراء الغربية"}',
                'latitude' => '24.50000000',
                'longitude' => '-13.00000000',
            ),
            244 =>
            array(
                'name' => 'Yemen',
                'iso3' => 'YEM',
                'iso2' => 'YE',
                'dial_code' => '967',
                'currency' => 'YER',
                'currency_symbol' => '﷼',
                'translations' => '{"br":"Iêmen","pt":"Iémen","nl":"Jemen","hr":"Jemen","fa":"یمن","de":"Jemen","es":"Yemen","fr":"Yémen","ja":"イエメン","it":"Yemen","ar":"اليمن"}',
                'latitude' => '15.00000000',
                'longitude' => '48.00000000',
            ),
            245 =>
            array(
                'name' => 'Zambia',
                'iso3' => 'ZMB',
                'iso2' => 'ZM',
                'dial_code' => '260',
                'currency' => 'ZMW',
                'currency_symbol' => 'ZK',
                'translations' => '{"br":"Zâmbia","pt":"Zâmbia","nl":"Zambia","hr":"Zambija","fa":"زامبیا","de":"Sambia","es":"Zambia","fr":"Zambie","ja":"ザンビア","it":"Zambia","ar":"زامبيا"}',
                'latitude' => '-15.00000000',
                'longitude' => '30.00000000',
            ),
            246 =>
            array(
                'name' => 'Zimbabwe',
                'iso3' => 'ZWE',
                'iso2' => 'ZW',
                'dial_code' => '263',
                'currency' => 'ZWL',
                'currency_symbol' => '$',
                'translations' => '{"br":"Zimbabwe","pt":"Zimbabué","nl":"Zimbabwe","hr":"Zimbabve","fa":"زیمباوه","de":"Simbabwe","es":"Zimbabue","fr":"Zimbabwe","ja":"ジンバブエ","it":"Zimbabwe","ar":"زيمبابوي"}',
                'latitude' => '-20.00000000',
                'longitude' => '30.00000000',
            ),
            247 =>
            array(
                'name' => 'Kosovo',
                'iso3' => 'XKX',
                'iso2' => 'XK',
                'dial_code' => '383',
                'currency' => 'EUR',
                'currency_symbol' => '€',
                'translations' => '{"ar":"Kosovo"}',
                'latitude' => '42.56129090',
                'longitude' => '20.34030350',
            ),
            248 =>
            array(
                'name' => 'Curaçao',
                'iso3' => 'CUW',
                'iso2' => 'CW',
                'dial_code' => '599',
                'currency' => 'ANG',
                'currency_symbol' => 'ƒ',
                'translations' => '{"br":"Curaçao","pt":"Curaçao","nl":"Curaçao","fa":"کوراسائو","de":"Curaçao","fr":"Curaçao","it":"Curaçao","ar":"Curaçao"}',
                'latitude' => '12.11666700',
                'longitude' => '-68.93333300',
            ),
            249 =>
            array(
                'name' => 'Sint Maarten (Dutch part)',
                'iso3' => 'SXM',
                'iso2' => 'SX',
                'dial_code' => '1721',
                'currency' => 'ANG',
                'currency_symbol' => 'ƒ',
                'translations' => '{"br":"Sint Maarten","pt":"São Martinho","nl":"Sint Maarten","fa":"سینت مارتن","de":"Sint Maarten (niederl. Teil)","fr":"Saint Martin (partie néerlandaise)","it":"Saint Martin (parte olandese)","ar":"Sint Maarten (Dutch part)"}',
                'latitude' => '18.03333300',
                'longitude' => '-63.05000000',
            ),
        ));
    }
}
