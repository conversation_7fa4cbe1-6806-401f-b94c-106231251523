<?php

namespace Pack\LaravelGeo\Models;

use Spa<PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class Country extends Model implements HasMedia
{
    use InteractsWithMedia, HasTranslations;

    public array $translatable = ['name'];

    protected $fillable = ['is_active'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function cities()
    {
        return config('geo.models.cities')
            ? $this->hasMany(config('geo.models.city_model'))
            : $this->hasMany(City::class)->whereRaw('1=0');
    }

    public function states()
    {
        return config('geo.models.states')
            ? $this->hasManyThrough(config('geo.models.state_model'), City::class)
            : $this->hasManyThrough(State::class, City::class)->whereRaw('1=0');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(config('geo.media.country_flag_collection', 'flags'))
            ->singleFile();
    }
}
