<?php

namespace Pack\LaravelGeo\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class City extends Model
{
    use HasTranslations;

    public array $translatable = ['name'];

    protected $fillable = ['is_active', 'state_id'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function state()
    {
        return config('geo.models.states')
            ? $this->belongsTo(config('geo.models.state_model'))
            : $this->belongsTo(State::class)->whereRaw('1=0');
    }
}
