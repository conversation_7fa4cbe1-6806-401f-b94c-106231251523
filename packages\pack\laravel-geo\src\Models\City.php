<?php

namespace Pack\LaravelGeo\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class City extends Model
{
    use HasTranslations;

    public array $translatable = ['name'];

    protected $fillable = ['is_active', 'country_id'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function country()
    {
        return config('geo.models.countries')
            ? $this->belongsTo(config('geo.models.country_model'))
            : $this->belongsTo(Country::class)->whereRaw('1=0');
    }

    public function states()
    {
        return config('geo.models.states')
            ? $this->hasMany(config('geo.models.state_model'))
            : $this->hasMany(State::class)->whereRaw('1=0');
    }
}
