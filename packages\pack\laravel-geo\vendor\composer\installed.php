<?php return array(
    'root' => array(
        'name' => 'pack-php/laravel-geo',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '*******',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'brick/math' => array(
            'pretty_version' => '0.13.1',
            'version' => '0.13.1.0',
            'reference' => 'fc7ed316430118cc7836bf45faff18d5dfc8de04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '*******',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.4',
            'version' => '3.4.4.0',
            'reference' => '198166618906cb2de69b95d7d47e5fa8aa1b2b95',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '6d6c96277ea252fc1304627204c3d5e6e15faa3b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '774ff2a22a93643d94bae8aad74fdc39a8c7084c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '8cd30f76947524e58a71ab610fee6aaa9eedc24b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'ec677967c1f2faf90b8428919124d2184a4c9b49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '488d00c42725ce0f6044a20961f24091ac2af6a3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'e1b1266e79e38998879123476618b9a8dce43629',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '0102688f22848ca3d622ddb7ddaf56b87e521cdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/database' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'f24c48dd8e38cabc01829b3c40d821bf52e18d6a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/database',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '2017f20e234e1be566c739fc9d4518e33a34ecc3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'd458bc675ced0b9db3ec4f10aa382b245a4b1cf6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'e862e5648ee34004fa56046b746f490dfa86c613',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => 'b6a14c20d69a44bf0a6fba664a00d23ca71770ee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '64e864d6a6b8a6b06d1e49f2ee0a7fd10edfd382',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v12.26.3',
            'version' => '*********',
            'reference' => '44d69e2784123f50b78a091874d66d18549b10d6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.6',
            'version' => '*******',
            'reference' => '86a8b692e8661d0fb308cec64f3d176821323077',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '*******',
            'reference' => 'b352cf0534aa1ae6b4d825d1e762e35d43f8a841',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.2.0',
            'version' => '*******',
            'reference' => '9712d8fa4cdf9240380b01eb4be55ad8dcf71416',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.10.2',
            'version' => '********',
            'reference' => '76b5c07b8a9d2025ed1610e14cef1f3fd6ad2c24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '*******',
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pack-php/laravel-geo' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '*******',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/image' => array(
            'pretty_version' => '3.8.5',
            'version' => '3.8.5.0',
            'reference' => 'a63f60b7387ebeacab463e79a95deb7ffed75430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/image-optimizer' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'reference' => '4fd22035e81d98fffced65a8c20d9ec4daa9671c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/image-optimizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-medialibrary' => array(
            'pretty_version' => '11.14.0',
            'version' => '11.14.0.0',
            'reference' => '7050a0d041be8c5c5ef5886967fbbbe578a54296',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-medialibrary',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-package-tools' => array(
            'pretty_version' => '1.92.7',
            'version' => '1.92.7.0',
            'reference' => 'f09a799850b1ed765103a4f0b4355006360c49a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-package-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-translatable' => array(
            'pretty_version' => '6.11.4',
            'version' => '6.11.4.0',
            'reference' => '032d85b28de315310dab2048b857016f1194f68b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-translatable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'spatie/temporary-directory' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'reference' => '580eddfe9a0a41a902cac6eeb8f066b42e65a32b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/temporary-directory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.3',
            'version' => '7.3.3.0',
            'reference' => 'cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.2',
            'version' => '7.3.2.0',
            'reference' => '2a6614966ba1074fa93dae0bc804227422df4dfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '380872130d3a5dd3ace2f4010d95125fde5d5c70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => '17f6f9a6b1735c0f163024d959f700cfbc5155e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php84' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'd8ced4d875142b6a7426000426b8abc631d6b191',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php84',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php85' => array(
            'pretty_version' => 'v1.33.0',
            'version' => '1.33.0.0',
            'reference' => 'd4e5fcd4ab3d998ab16c0db48e6cbb9a01993f91',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php85',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.3',
            'version' => '7.3.3.0',
            'reference' => '32241012d521e2e8a9d713adb0812bb773b907f1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.3',
            'version' => '7.3.3.0',
            'reference' => '17a426cce5fd1f0901fefa9b2a490d0038fd3c9c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.3',
            'version' => '7.3.3.0',
            'reference' => 'e0837b4cbcef63c754d89a4806575cada743a38d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
