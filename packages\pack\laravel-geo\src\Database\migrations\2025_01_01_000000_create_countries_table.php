<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (config('geo.models.countries')) {
            Schema::create('countries', function (Blueprint $table) {
                $table->id();
                $table->json('name'); // Spatie Translatable stores translations as JSON.
                $table->string('iso2', 2)->nullable(); // ISO code like "US"
                $table->string('iso3', 3)->nullable(); // ISO code like "USA"
                $table->string('dial_code', 10)->nullable(); // like +1
                $table->string('currency', 3)->nullable(); // like USD
                $table->string('currency_symbol', 10)->nullable(); // like $
                $table->string('latitude')->nullable();
                $table->string('longitude')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        if (config('geo.models.countries')) {
            Schema::dropIfExists('countries');
        }
    }
};
