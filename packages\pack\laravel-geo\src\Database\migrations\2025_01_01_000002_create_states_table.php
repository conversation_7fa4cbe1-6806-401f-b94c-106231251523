<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (config('geo.models.states')) {
            Schema::create('states', function (Blueprint $table) {
                $table->id();
                $table->json('name'); // Spatie Translatable stores translations as JSON
                $table->string('iso2', 2)->nullable(); // ISO code like "US"        
                $table->string('latitude')->nullable();
                $table->string('longitude')->nullable();
                $table->boolean('is_active')->default(true);

                if (config('geo.models.countires')) {
                    $table->foreignId('country_id')
                        ->constrained('countires')
                        ->cascadeOnDelete();
                } else {
                    $table->unsignedBigInteger('country_id')->nullable();
                }

                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        if (config('geo.models.states')) {
            Schema::dropIfExists('states');
        }
    }
};
