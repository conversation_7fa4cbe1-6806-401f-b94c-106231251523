<?php

namespace Pack\LaravelGeo\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class State extends Model
{
    use HasTranslations;

    public array $translatable = ['name'];

    protected $fillable = ['is_active', 'country_id'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function country()
    {
        return config('geo.models.countries')
            ? $this->belongsTo(config('geo.models.country_model'))
            : $this->belongsTo(Country::class)->whereRaw('1=0');
    }

    public function cities()
    {
        return config('geo.models.cities')
            ? $this->hasMany(config('geo.models.city_model'))
            : $this->hasMany(City::class)->whereRaw('1=0');
    }
}
