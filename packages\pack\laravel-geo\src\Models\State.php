<?php

namespace Pack\LaravelGeo\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class State extends Model
{
    use HasTranslations;

    public array $translatable = ['name'];

    protected $fillable = ['is_active', 'city_id'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function city()
    {
        return config('geo.models.cities')
            ? $this->belongsTo(config('geo.models.city_model'))
            : $this->belongsTo(City::class)->whereRaw('1=0');
    }
}
