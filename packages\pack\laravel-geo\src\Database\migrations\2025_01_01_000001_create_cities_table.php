<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (config('geo.models.cities')) {
            Schema::create('cities', function (Blueprint $table) {
                $table->id();
                $table->json('name'); // Spatie Translatable stores translations as JSON
                $table->boolean('is_active')->default(true);

                if (config('geo.models.countries')) {
                    $table->foreignId('country_id')
                        ->constrained('countries')
                        ->cascadeOnDelete();
                } else {
                    $table->unsignedBigInteger('country_id')->nullable();
                }

                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        if (config('geo.models.cities')) {
            Schema::dropIfExists('cities');
        }
    }
};
