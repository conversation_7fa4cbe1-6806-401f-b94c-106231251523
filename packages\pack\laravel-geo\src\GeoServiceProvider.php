<?php

namespace Pack\LaravelGeo;

use Illuminate\Support\ServiceProvider;

class GeoServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->mergeConfigFrom(__DIR__ . '/../config/geo.php', 'geo');
    }

    public function boot()
    {
        // Publish config
        $this->publishes([
            __DIR__ . '/../config/geo.php' => config_path('geo.php'),
        ], 'geo-config');

        // Migrations
        $this->loadMigrationsFrom(__DIR__ . '/Database/migrations');
        $this->publishes([
            __DIR__ . '/Database/migrations' => database_path('migrations'),
        ], 'geo-migrations');

        // Seeders
        $this->publishes([
            __DIR__ . '/Database/seeders' => database_path('seeders/vendor/pack/laravel-geo'),
        ], 'geo-seeders');

        // Flags
        $this->publishes([
            __DIR__ . '/../resources/flags' => public_path('vendor/laravel-geo/flags'),
        ], 'geo-flags');
    }
}
